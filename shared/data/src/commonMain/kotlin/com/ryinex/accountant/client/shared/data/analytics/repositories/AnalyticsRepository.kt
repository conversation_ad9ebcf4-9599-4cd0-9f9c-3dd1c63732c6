package com.ryinex.accountant.client.shared.data.analytics.repositories

import com.ryinex.accountant.client.shared.data.analytics.models.HomeSummary
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async

class AnalyticsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {

    suspend fun getHomeSummary(): HomeSummary = logger.async {
        val url = "$apiBaseUrl/api/v1/analytics/home"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(HomeSummary.serializer())
        return@async response
    }
}