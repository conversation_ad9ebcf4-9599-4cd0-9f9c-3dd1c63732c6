package com.ryinex.accountant.client.shared.data.auth.repositories

import com.ryinex.accountant.client.shared.data.auth.models.AuthResponse
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequest
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequestOrganization
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequestUser
import com.ryinex.accountant.client.shared.data.auth.models.RegisterResponse
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class AuthRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {
    val authStream = MutableStateFlow<AuthResponse?>(null)

    suspend fun login(username: String, password: String, organizationCode: String): AuthResponse = logger.async {
        val url = "$apiBaseUrl/api/v1/auth/login"
        val body = buildJsonObject {
            put("username", username.trim())
            put("password", password.trim())
            put("organization_code", organizationCode.trim())
        }
        val request = HttpRequest(
            url = url,
            body = body,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            )
        )
        val response = httpClient.request(request).value(AuthResponse.serializer())
        UserSettings.userAccessToken.set("Bearer ${response.token}")
        authStream.emit(response)

        return@async response
    }

    suspend fun register(registerRequest: RegisterRequest) = logger.async {
        val url = "$apiBaseUrl/api/v1/auth/register"
        val body = buildJsonObject {
            put("user", json.encodeToJsonElement(RegisterRequestUser.serializer(), registerRequest.user))
            put("organization", json.encodeToJsonElement(RegisterRequestOrganization.serializer(), registerRequest.organization))
        }
        val request = HttpRequest(
            url = url,
            body = body,
            method = HttpRequest.Method.POST,
            headers = listOf(HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json"))
        )

        httpClient.request(request).value(RegisterResponse.serializer())
    }

    fun logout() = logger.log {
        UserSettings.userAccessToken.remove()
        UserSettings.loggedInUser.remove()
    }
}