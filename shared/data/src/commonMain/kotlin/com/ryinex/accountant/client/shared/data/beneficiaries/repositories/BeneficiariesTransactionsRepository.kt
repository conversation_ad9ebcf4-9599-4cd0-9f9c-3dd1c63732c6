package com.ryinex.accountant.client.shared.data.beneficiaries.repositories

import com.ryinex.accountant.client.shared.data.beneficiaries.models.BeneficiaryTransaction
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class BeneficiariesTransactionsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {
    suspend fun update(
        transactionId: Long,
        description: String,
        transactionDate: String,
        version: Long,
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/beneficiaries/transactions/$transactionId"
        val body = buildJsonObject {
            put("description", description.trim())
            put("transactionDate", transactionDate.trim())
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request)
        return@async response.value(BeneficiaryTransaction.serializer())
    }
}