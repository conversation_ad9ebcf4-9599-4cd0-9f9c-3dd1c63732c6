package com.ryinex.accountant.client.shared.data.common.settings

import com.russhwolf.settings.Settings
import com.russhwolf.settings.serialization.decodeValueOrNull
import com.russhwolf.settings.serialization.encodeValue
import com.ryinex.accountant.client.shared.data.utilis.decrypt
import com.ryinex.accountant.client.shared.data.utilis.encrypt
import core.common.serialization.json
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.serialization.KSerializer
import kotlinx.serialization.builtins.serializer

open class ItemSettingsProperty<T>(val serializer: KSerializer<*>, val key: String, val default: T?, val settings: Settings) {
    private val state: MutableStateFlow<T> = MutableStateFlow(get())
    val stream = state.asStateFlow()

    fun get(): T = (settings.decode(serializer, key) ?: default ) as T
    fun set(value: T) {
        settings.encode(serializer as KSerializer<T>, key, value)
        state.update { value }
    }
    fun remove() {
        settings.removeValueEncrypted(key)
        state.update { default as T }
    }
}

class StringSettingsProperty(key: String, default: String, settings: Settings) : ItemSettingsProperty<String>(String.serializer(), key, default, settings)
class IntSettingsProperty(key: String, default: Int, settings: Settings) : ItemSettingsProperty<Int>(Int.serializer(), key, default, settings)
class LongSettingsProperty(key: String, default: Long, settings: Settings) : ItemSettingsProperty<Long>(Long.serializer(), key, default, settings)
class BooleanSettingsProperty(key: String, default: Boolean, settings: Settings) : ItemSettingsProperty<Boolean>(Boolean.serializer(), key, default, settings)
class DoubleSettingsProperty(key: String, default: Double, settings: Settings) : ItemSettingsProperty<Double>(Double.serializer(), key, default, settings)

private fun <T> Settings.decode(serializer: KSerializer<T>, key: String): T? {
    return runCatching<T?> { decodeValueOrNull<String>(key.encrypt())?.let { json.decodeFromString(serializer, it.decrypt()) } }.getOrNull()
}

private fun <T> Settings.encode(serializer: KSerializer<T>, key: String, value: T) {
    encodeValue(key.encrypt(), json.encodeToString(serializer, value).encrypt())
}

fun Settings.removeValueEncrypted(key: String) {
    remove(key.encrypt())
}
