package com.ryinex.accountant.client.shared.data.common.settings

import com.russhwolf.settings.Settings
import com.russhwolf.settings.serialization.decodeValueOrNull
import com.russhwolf.settings.serialization.encodeValue
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.data.utilis.decrypt
import com.ryinex.accountant.client.shared.data.utilis.encrypt
import core.common.serialization.json
import kotlinx.coroutines.flow.MutableStateFlow

object UserSettings {
    private val settings = Settings()

    val userAccessToken = StringSettingsProperty(userAccessTokenKey, "", settings)
    val organization = ItemSettingsProperty<Organization?>(Organization.serializer(), organizationKey, null, settings)
    val loggedInUser = ItemSettingsProperty<User?>(User.serializer(), loggedInUserKey, null, settings)
}

private const val userAccessTokenKey = "userAccessToken"
private const val organizationKey = "organization"
private const val loggedInUserKey = "loggedInUser"