package com.ryinex.accountant.client.shared.data.customers.repositories

import com.ryinex.accountant.client.shared.data.customers.models.CustomerTransaction
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class CustomersTransactionsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {
    suspend fun create(
        customerId: Long,
        amount: AppDouble,
        description: String,
        projectId: Long,
        transactionDateIseoString: String
    ): CustomerTransaction = logger.async {
        val url = "$apiBaseUrl/api/v1/customers/$customerId/transactions"
        val body = buildJsonObject {
            put("amount", amount)
            put("description", description.trim())
            put("transactionDate", transactionDateIseoString.trim())
            put("projectId", projectId)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(CustomerTransaction.serializer())
        return@async response
    }

    suspend fun getTransactionsByCustomerId(customerId: Long): List<CustomerTransaction> = logger.async {
        val url = "$apiBaseUrl/api/v1/customers/$customerId/transactions"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(ListSerializer(CustomerTransaction.serializer()))
        return@async response.sortedByDescending { it.createdAt }
    }

    suspend fun pay(
        customerId: Long,
        transactionId: Long,
        amount: AppDouble,
        version: Long
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/customers/$customerId/transactions/$transactionId/pay"
        val body = buildJsonObject {
            put("amountPaid", amount)
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request)
        return@async response.value(CustomerTransaction.serializer())
    }

    suspend fun update(
        customerId: Long,
        transactionId: Long,
        description: String,
        version: Long,
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/customers/$customerId/transactions/$transactionId"
        val body = buildJsonObject {
            put("description", description.trim())
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request)
        return@async response.value(CustomerTransaction.serializer())
    }
}