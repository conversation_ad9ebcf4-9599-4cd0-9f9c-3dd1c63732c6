package com.ryinex.accountant.client.shared.data.notes.repositories

import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import com.ryinex.accountant.client.shared.data.notes.models.Note
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class NotesRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {

    suspend fun getBeneficiaryNotes(beneficiaryId: Long) = logger.async {
        val url = "$apiBaseUrl/api/v1/notes/beneficiaries/$beneficiaryId"
        val request = HttpRequest(
            headers = listOf(HttpHeader(HttpHeader.Type.AUTHORIZATION, UserSettings.userAccessToken.get())),
            method = HttpRequest.Method.GET,
            url = url
        )

        val result = httpClient.request(request)

        return@async result.value(ListSerializer(Note.serializer())).sortedByDescending { it.createdAt }
    }

    suspend fun getCustomerNotes(customerId: Long) = logger.async {
        val url = "$apiBaseUrl/api/v1/notes/customers/$customerId"
        val request = HttpRequest(
            headers = listOf(HttpHeader(HttpHeader.Type.AUTHORIZATION, UserSettings.userAccessToken.get())),
            method = HttpRequest.Method.GET,
            url = url
        )

        val result = httpClient.request(request)

        return@async result.value(ListSerializer(Note.serializer())).sortedByDescending { it.createdAt }
    }

    suspend fun getProjectNotes(projectId: Long) = logger.async {
        val url = "$apiBaseUrl/api/v1/notes/projects/$projectId"
        val request = HttpRequest(
            headers = listOf(HttpHeader(HttpHeader.Type.AUTHORIZATION, UserSettings.userAccessToken.get())),
            method = HttpRequest.Method.GET,
            url = url
        )

        val result = httpClient.request(request)

        return@async result.value(ListSerializer(Note.serializer())).sortedByDescending { it.createdAt }
    }

    suspend fun create(note: String, beneficiaryId: Long?, customerId: Long?, projectId: Long?) = logger.async {
        val url = "$apiBaseUrl/api/v1/notes"
        val body = buildJsonObject {
            put("note", note)
            put("beneficiaryId", beneficiaryId)
            put("customerId", customerId)
            put("projectId", projectId)
        }
        val request = HttpRequest(
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, UserSettings.userAccessToken.get()),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json")
            ),
            method = HttpRequest.Method.POST,
            url = url,
            body = body
        )

        val result = httpClient.request(request)

        return@async result.value(Note.serializer())
    }

    suspend fun edit(
        note: String,
        noteId: Long,
        beneficiaryId: Long?,
        customerId: Long?,
        projectId: Long?,
        version: Long
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/notes/$noteId"
        val body = buildJsonObject {
            put("note", note)
            put("beneficiaryId", beneficiaryId)
            put("customerId", customerId)
            put("projectId", projectId)
            put("version", version)
        }
        val request = HttpRequest(
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, UserSettings.userAccessToken.get()),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json")
            ),
            method = HttpRequest.Method.PATCH,
            url = url,
            body = body
        )

        val result = httpClient.request(request)

        return@async result.value(Note.serializer())
    }

    suspend fun delete(
        noteId: Long
    ): Unit = logger.async {
        val url = "$apiBaseUrl/api/v1/notes/$noteId"
        val request = HttpRequest(
            headers = listOf(HttpHeader(HttpHeader.Type.AUTHORIZATION, UserSettings.userAccessToken.get())),
            method = HttpRequest.Method.DELETE,
            url = url
        )

        val result = httpClient.request(request)
    }
}