package com.ryinex.accountant.client.shared.data.organization.repositories

import com.ryinex.accountant.client.shared.data.organization.models.OrganizationCapitalTransaction
import com.ryinex.accountant.client.shared.data.organization.models.OrganizationCapitalTransactionDto
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class CapitalTransactionsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger,
) {

    suspend fun add(
        organizationId: Long,
        amount: AppDouble,
        description: String,
        transactionDateIseoString: String
    ): OrganizationCapitalTransaction = logger.async {
        val url = "$apiBaseUrl/api/v1/organizations/$organizationId/capital/add"
        val body = buildJsonObject {
            put("amount", amount)
            put("description", description.trim())
            put("transactionDate", transactionDateIseoString.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                ),
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            ),
            body = body
        )

        val response = httpClient.request(request).value(OrganizationCapitalTransactionDto.serializer())
        return@async response.toModel()
    }

    suspend fun remove(
        organizationId: Long,
        amount: AppDouble,
        description: String,
        transactionDateIseoString: String
    ): OrganizationCapitalTransaction = logger.async {
        val url = "$apiBaseUrl/api/v1/organizations/$organizationId/capital/remove"
        val body = buildJsonObject {
            put("amount", amount)
            put("description", description.trim())
            put("transactionDate", transactionDateIseoString.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                ),
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            ),
            body = body
        )

        val response = httpClient.request(request).value(OrganizationCapitalTransactionDto.serializer())
        return@async response.toModel()
    }

    suspend fun getTransactionsOfOrganization(
        organizationId: Long
    ): List<OrganizationCapitalTransaction> = logger.async {
        val url = "$apiBaseUrl/api/v1/organizations/$organizationId/capital"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                )
            )
        )
        val response = httpClient.request(request).value(ListSerializer(OrganizationCapitalTransactionDto.serializer()))

        return@async response.sortedByDescending { it.createdAt }.map { it.toModel() }
    }
}