package com.ryinex.accountant.client.shared.data.organization.repositories

import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async

class OrganizationsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {

    suspend fun getOrganization(): Organization = logger.async {
        val url = "$apiBaseUrl/api/v1/organizations/organization"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                )
            )
        )
        val response = httpClient.request(request).value(Organization.serializer())
        return@async response
    }
}