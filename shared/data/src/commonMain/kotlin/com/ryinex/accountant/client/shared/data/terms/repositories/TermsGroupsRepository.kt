package com.ryinex.accountant.client.shared.data.terms.repositories

import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class TermsGroupsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {

    suspend fun create(
        name: String,
        description: String,
    ): TermsGroup = logger.async {
        val url = "$apiBaseUrl/api/v1/terms/groups"
        val body = buildJsonObject {
            put("name", name.trim())
            put("description", description.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(TermsGroup.serializer())
        return@async response
    }

    suspend fun getGroupsOfOrganization(): List<TermsGroup> = logger.async {
        val url = "$apiBaseUrl/api/v1/terms/groups"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(ListSerializer(TermsGroup.serializer()))

        return@async response.sortedBy { it.name }
    }

    suspend fun edit(
        name: String,
        description: String,
        termsGroupId: Long,
        version: Long,
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/terms/groups/$termsGroupId"
        val body = buildJsonObject {
            put("name", name.trim())
            put("description", description.trim())
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(TermsGroup.serializer())
        return@async response
    }
}