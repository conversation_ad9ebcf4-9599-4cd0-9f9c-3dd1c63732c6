package com.ryinex.accountant.client.shared.data.terms.repositories

import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class TermsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {

    suspend fun create(
        groupId: Long,
        name: String,
        description: String,
    ): Term = logger.async {
        val url = "$apiBaseUrl/api/v1/terms/groups/$groupId"
        val body = buildJsonObject {
            put("name", name.trim())
            put("description", description.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(Term.serializer())
        return@async response
    }

    suspend fun getTermsOfOrganization(): List<Term> = logger.async {
        val url = "$apiBaseUrl/api/v1/terms"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(ListSerializer(Term.serializer()))
        return@async response.sortedBy { it.name }
    }

    suspend fun getTermsByGroupId(groupId: Long): List<Term> = logger.async {
        val url = "$apiBaseUrl/api/v1/terms/groups/$groupId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(ListSerializer(Term.serializer()))
        return@async response.sortedBy { it.name }
    }

    suspend fun edit(name: String, description: String, termId: Long, version: Long): Term = logger.async {
        val url = "$apiBaseUrl/api/v1/terms/$termId"
        val body = buildJsonObject {
            put("name", name.trim())
            put("description", description.trim())
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(Term.serializer())
        return@async response
    }
}