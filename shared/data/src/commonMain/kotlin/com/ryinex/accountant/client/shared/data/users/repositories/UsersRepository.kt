package com.ryinex.accountant.client.shared.data.users.repositories

import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.data.users.models.UserProjectAccess
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequestUser
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import com.ryinex.accountant.client.shared.data.users.models.BalanceTransaction
import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class UsersRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {
    fun getLoggedInUser(): User = logger.log {
        return@log UserSettings.loggedInUser.get()!!
    }

    fun isLoggedIn(): Boolean = logger.log {
        return@log UserSettings.loggedInUser.get() != null
    }

    suspend fun createUser(
        username: String,
        name: String,
        phoneNumber: String,
        isAdmin: Boolean,
        secondaryPhoneNumber: String,
        password: String
    ): User = logger.async {
        val url = "$apiBaseUrl/api/v1/users"
        val body = RegisterRequestUser(
            name = name,
            username = username,
            phoneNumber = phoneNumber,
            password = password,
            confirmPassword = password,
            isAdmin = isAdmin
        )

        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                ),
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            ),
            body = json.encodeToJsonElement(RegisterRequestUser.serializer(), body)
        )

        val response = httpClient.request(request).value(User.serializer())
        return@async response
    }

    suspend fun increaseUserBalance(userId: Long, amount: AppDouble): Unit = logger.async {
        val url = "$apiBaseUrl/api/v1/users/${userId}/balance/increase"
        val body = buildJsonObject {
            put("amount", amount)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                ),
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            ),
            body = body
        )

        val response = httpClient.request(request)
    }

    suspend fun decreaseUserCustody(userId: Long, custody: AppDouble) = logger.async {
        val url = "$apiBaseUrl/api/v1/users/${userId}/balance/decrease"
        val body = buildJsonObject {
            put("amount", custody)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                ),
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            ),
            body = body
        )

        val response = httpClient.request(request)
        return@async response
    }

    suspend fun getUserById(userId: Long): User = logger.async {
        val url = "$apiBaseUrl/api/v1/users/${userId}"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                )
            )
        )
        val response = httpClient.request(request).value(User.serializer())
        return@async response
    }

    suspend fun getUsersOfOrganization(): List<User> = logger.async {
        val url = "$apiBaseUrl/api/v1/users"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                )
            )
        )
        return@async httpClient.request(request).value(ListSerializer(User.serializer())).sortedBy { it.name }
    }

    suspend fun getUsersAccessOfProject(projectId: Long): List<UserProjectAccess> = logger.async {
        val url = "$apiBaseUrl/api/v1/users/projects/$projectId/access"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )

        val response = httpClient.request(request)
        return@async response.value(ListSerializer(UserProjectAccess.serializer())).sortedBy { it.user.name }
    }

    suspend fun getUserProjectAccessByUserIdAndProjectId(
        userId: Long,
        projectId: Long
    ): UserProjectAccess = logger.async {
        val url = "$apiBaseUrl/api/v1/users/$userId/projects/$projectId/access"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )

        val response = httpClient.request(request)
        return@async response.value(UserProjectAccess.serializer())
    }

    suspend fun getBalanceTransactions(userId: Long) = logger.async {
        val url = "$apiBaseUrl/api/v1/users/$userId/balance/transactions"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )

        val response = httpClient.request(request)
        return@async response.value(ListSerializer(BalanceTransaction.serializer())).sortedByDescending { it.createdAt }
    }

    suspend fun deleteUser(userId: Long) = logger.async {
        val url = "$apiBaseUrl/api/v1/users/$userId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.DELETE,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            )
        )

        httpClient.request(request)
    }
}