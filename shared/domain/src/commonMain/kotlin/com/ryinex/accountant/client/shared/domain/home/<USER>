package com.ryinex.accountant.client.shared.domain.home

import cafe.adriel.voyager.core.model.ScreenModel
import cafe.adriel.voyager.core.model.screenModelScope
import com.ryinex.accountant.client.shared.data.analytics.repositories.AnalyticsRepository
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import com.ryinex.accountant.client.shared.domain.home.repositories.ScreenStateRepository
import com.ryinex.accountant.client.shared.data.projects.repositories.ProjectsRepository
import com.ryinex.accountant.client.shared.data.users.repositories.UsersRepository
import com.ryinex.accountant.client.shared.data.auth.repositories.AuthRepository
import com.ryinex.accountant.client.shared.domain.welcome.login.handle
import com.ryinex.accountant.client.shared.data.common.utilities.DeferHolder
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext

class ViewModel(
    private val status: StatusRepository,
    private val projects: ProjectsRepository,
    private val users: UsersRepository,
    private val analytics: AnalyticsRepository,
    private val auth: AuthRepository,
    private val coroutinesContext: CoroutineContext
) : ScreenModel {
    private val scope = screenModelScope + coroutinesContext

    val screenState = ScreenStateRepository(status)

    fun initScreen() = scope.handle {
        refreshScreen()
    }

    fun refreshScreen() = scope.handle {
        val result = status.execute(
            loading = Message.fromString("جاري تحميل المشاريع ..."),
            success = Message.fromString("تم تحميل المشاريع"),
            job = {
                screenState.update { it.copy(loggedInUser = users.getLoggedInUser()) }
                val defers = DeferHolder(this)
                val projects = defers.add { projects.getProjectsOfOrganization() }
                val analytics = defers.add { analytics.getHomeSummary() }
                defers.awaitAll()

                screenState.update { it.copy(projects = projects.value, analytics = analytics.value) }
            }
        )

        if (result is AsyncResult.Fail) throw result.error
    }

    fun logout() {
        auth.logout()

    }
}