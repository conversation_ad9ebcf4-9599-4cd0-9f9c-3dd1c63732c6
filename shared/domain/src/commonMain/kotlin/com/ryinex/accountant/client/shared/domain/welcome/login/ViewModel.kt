package com.ryinex.accountant.client.shared.domain.welcome.login

import cafe.adriel.voyager.core.model.ScreenModel
import cafe.adriel.voyager.core.model.screenModelScope
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import com.ryinex.accountant.client.shared.data.users.repositories.UsersRepository
import com.ryinex.accountant.client.shared.data.auth.repositories.AuthRepository
import com.ryinex.accountant.client.shared.domain.welcome.login.models.SideEffect
import com.ryinex.accountant.client.shared.domain.welcome.login.repositories.ScreenStateRepository
import com.ryinex.accountant.client.shared.domain.welcome.login.repositories.SideEffectsRepository
import com.ryinex.accountant.client.shared.data.common.error.ErrorHandler
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI.errorHandler
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI.logger
import com.ryinex.accountant.client.shared.data.organization.repositories.OrganizationsRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext

class ViewModel(
    private val status: StatusRepository,
    private val users: UsersRepository,
    private val auth: AuthRepository,
    private val organization: OrganizationsRepository,
    private val errorHandler: ErrorHandler,
    private val logger: Logger,
    private val coroutinesContext: CoroutineContext,
) : ScreenModel {
    private val scope = screenModelScope + coroutinesContext

    val screenState = ScreenStateRepository(status)
    val sideEffects = SideEffectsRepository()

    fun isLoggedIn() = logger.log {
        return@log users.isLoggedIn()
    }

    fun login(username: String, password: String, organizationCode: String) = scope.handle {
        val result = status.execute(
            loading = Message.fromString("جاري تسجيل الدخول ..."),
            success = Message.fromString("تم تسجيل الدخول"),
            job = { loginJob(username, password, organizationCode) }
        )

        if (result is AsyncResult.Fail) throw result.error

        screenState.update { it.copy(savedOrganization = UserSettings.organization.get()) }
    }

    fun changeSavedOrganization() = scope.handle {
        screenState.update { it.copy(savedOrganization = null) }
    }

    private suspend fun loginJob(username: String, password: String, organizationCode: String) = logger.async {
        val auth = auth.login(username = username, password = password, organizationCode = organizationCode)
        val user = users.getUserById(auth.userId)
        UserSettings.loggedInUser.set(user)
        UserSettings.organization.set(organization.getOrganization())
        sideEffects.emit(SideEffect.LoginSuccess(user))
    }

}


inline fun CoroutineScope.handle(crossinline block: suspend CoroutineScope.() -> Unit) {
    launch {
        errorHandler.execute { logger.async(block = { block() }) }
    }
}

inline fun <T> CoroutineScope.deferred(crossinline block: suspend CoroutineScope.() -> T): Deferred<T?> {
    return async {
        errorHandler.execute { logger.async(block = { block() }) }
    }
}