package com.ryinex.accountant.client.shared.domain.welcome.login.repositories

import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import com.ryinex.accountant.client.shared.domain.common.state.StateRepository
import core.common.status.StatusRepository
import com.ryinex.accountant.client.shared.domain.welcome.login.models.ScreenState
import core.common.status.Status
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

class ScreenStateRepository(private val status: StatusRepository) : StateRepository<ScreenState>(
    initState()
) {

    override fun producer(init: Flow<ScreenState>): Flow<ScreenState> {
        return combine(init, status.stream) { state, status ->
            state.copy(status = status.filter { it !is Status.Success })
        }
    }
}

private fun initState(): ScreenState {
    return ScreenState(
        savedOrganization = UserSettings.organization.get(),
        status = listOf()
    )
}