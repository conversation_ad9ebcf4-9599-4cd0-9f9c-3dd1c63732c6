package com.ryinex.accountant.client.shared.presentation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.LayoutDirection
import cafe.adriel.voyager.navigator.Navigator
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import com.ryinex.accountant.client.shared.presentation.home.presentation.ui.HomeScreen
import com.ryinex.accountant.client.shared.presentation.login.presentation.LoginScreen
import com.ryinex.accountant.client.shared.presentation.theme.AppTheme
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
@Preview
fun App() {
    AppTheme {
        CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Rtl) {
            Box(
                modifier = Modifier.safeDrawingPadding()
            ) {
                val loggedInUser = UserSettings.loggedInUser.stream.collectAsState().value

                LaunchedEffect(loggedInUser) {
                    println("[LOGGY] Logged in user: $loggedInUser")
                }

                if (loggedInUser != null) {
                    Navigator(HomeScreen())
                } else {
                    Navigator(LoginScreen(null, null))
                }
            }
        }
    }
}