package com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui

import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.ui.Modifier
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.feature.components.adaptWidth
import com.ryinex.accountant.client.shared.presentation.customers.presentation.ui.CustomerCard
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.domain.beneficiaries.home.ViewModel
import com.ryinex.accountant.client.shared.domain.beneficiaries.home.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.components.SearchBar

internal fun LazyStaggeredGridScope.BeneficiariesListSection(
    viewModel: ViewModel,
    state: ScreenState
) {

    item(span = StaggeredGridItemSpan.FullLine) {
        SearchBar(
            modifier = Modifier.adaptWidth(),
            onValueChange = { viewModel.search(it) },
            value = state.search,
            enabled = true
        )
    }

    state.filteredBeneficiaries.forEach { beneficiary ->
        item {
            val navigator = LocalNavigator.current

            CustomerCard(
                modifier = Modifier.adaptWidth(),
                name = beneficiary.name,
                phoneNumber = beneficiary.phoneNumber,
                enabled = state.isBeneficiaryDetailsEnabled,
                onClick = { navigator?.browserPush(BeneficiaryTransactionsScreen(beneficiary)) }
            )
        }
    }
}