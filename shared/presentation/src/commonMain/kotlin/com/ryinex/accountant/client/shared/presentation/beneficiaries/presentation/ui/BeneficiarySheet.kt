package com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppPhoneNumberTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.validName
import com.ryinex.accountant.client.shared.presentation.common.texts.validPhoneNumber
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.execute
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

private class StateHolder(
    val name: StringState = StringState(""),
    val phoneNumber: StringState = StringState(""),
    val isLoading: BooleanState = BooleanState(false)
) {
    val isNameError = DerivedState(true, name.stream.map { it.validName() })
    val isPhoneNumberError = DerivedState(true, phoneNumber.stream.map { it.validPhoneNumber() })

    private val actionEnabledStream = combine(
        isNameError.stream,
        isPhoneNumberError.stream,
        isLoading.stream
    ) { nameError, phoneError, loading ->
        !loading && !nameError && !phoneError
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)

    fun fromBeneficiary(beneficiary: Beneficiary) {
        name.update(beneficiary.name)
        phoneNumber.update(beneficiary.phoneNumber)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BeneficiarySheet(
    beneficiary: Beneficiary? = null,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val state = remember { StateHolder() }

    LaunchedEffect(Unit) {
        if (beneficiary != null) state.fromBeneficiary(beneficiary)
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                beneficiary = beneficiary,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish
            )
        }
    }
}

private fun LazyListScope.SheetContent(
    state: StateHolder,
    beneficiary: Beneficiary?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (beneficiary != null) "تعديل مستفيد" else "إضافة مستفيد"
        AppSectionTitleText(title)
    }

    item {
        ActionsRow(
            state = state,
            beneficiary = beneficiary,
            onDismissRequest = onDismissRequest,
            onFinish = onFinish
        )
    }

    item { NameInput(state = state) }

    item { PhoneNumberInput(state = state) }
}

@Composable
private fun NameInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.name.listen,
        hint = "الاسم",
        enabled = !state.isLoading.listen,
        isError = state.isNameError.listen,
        onValueChange = { state.name.update(it) },
        errorText = "الرجاء إدخال إسم صحيح"
    )
}

@Composable
private fun PhoneNumberInput(state: StateHolder) {
    AppPhoneNumberTextField(
        modifier = Modifier.fillMaxWidth(),
        phoneNumber = state.phoneNumber.listen,
        enabled = !state.isLoading.listen,
        isError = state.isPhoneNumberError.listen,
        phoneNumberLength = 11,
        onValueChange = { state.phoneNumber.update(it) }
    )
}

@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: StateHolder,
    beneficiary: Beneficiary?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
        alignment = Alignment.CenterVertically
    ) {
        AppTextButton(
            modifier = Modifier,
            text = "إلغاء",
            enabled = !state.isLoading.listen,
            onClick = onDismissRequest
        )

        AppTextButton(
            modifier = Modifier,
            text = if (beneficiary != null) "تعديل" else "إضافة",
            enabled = state.actionEnabled.listen,
            onClick = {
                scope.launch {
                    if (beneficiary != null) {
                        updateBeneficiary(state = state, beneficiary = beneficiary, onFinish = onFinish)
                    } else {
                        addBeneficiary(state = state, onFinish = onFinish)
                    }
                    onDismissRequest()
                }
            }
        )
    }
}

private suspend fun addBeneficiary(
    state: StateHolder,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة المستفيد ...",
        success = "تم إضافة المستفيد",
        job = { addBeneficiaryJob(state = state, onFinish = onFinish) }
    )
}

private suspend fun addBeneficiaryJob(
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.beneficiaries.createBeneficiary(
        name = state.name.get(),
        phoneNumber = state.phoneNumber.get(),
        secondaryPhoneNumber = state.phoneNumber.get()
    )

    onFinish()
}

private suspend fun updateBeneficiary(
    state: StateHolder,
    beneficiary: Beneficiary,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل المستفيد ...",
        success = "تم تعديل المستفيد",
        job = { updateBeneficiaryJob(beneficiary = beneficiary, state = state, onFinish = onFinish) }
    )
}

private suspend fun updateBeneficiaryJob(
    beneficiary: Beneficiary,
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.beneficiaries.editBeneficiary(
        beneficiaryId = beneficiary.id,
        name = state.name.get(),
        phoneNumber = state.phoneNumber.get(),
        version = beneficiary.version
    )

    onFinish()
}

private suspend fun <T> jobWrapper(state: StateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}