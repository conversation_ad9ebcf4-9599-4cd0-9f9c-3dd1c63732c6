package com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_edit
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.resources.ic_add
import com.ryinex.accountant.client.resources.ic_export
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.domain.beneficiaries.details.ViewModel
import com.ryinex.accountant.client.shared.domain.beneficiaries.details.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.presentation.common.components.FiltersSection
import com.ryinex.accountant.client.shared.presentation.common.components.SearchBar
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.tables.projectExpenseTable
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.notes.AddNoteSheet
import com.ryinex.accountant.client.shared.presentation.notes.NotesSection
import com.ryinex.accountant.client.shared.presentation.projects.presentation.ui.ProjectExpenseSheet
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource

class BeneficiaryTransactionsScreen(private val beneficiary: Beneficiary) : BrowserScreen {

    override val urlSegment: String = "${beneficiary.id}"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                transactions = TempDI.projects,
                beneficiaries = TempDI.beneficiaries,
                users = TempDI.users,
                terms = TempDI.terms,
                notes = TempDI.notes,
                termsGroups = TempDI.termsGroups,
                coroutinesContext = emptyCoroutineContext
            )
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initTransactionsState).value
        LaunchedEffect(Unit) { viewModel.initScreen(beneficiary) }

        AppBasePage(
            title = "المستفيدين",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = state.isBackEnabled,
            isPullToRefreshEnabled = true,
            isPullToRefresh = !state.isBackEnabled,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = { navigator?.browserPop() }
        ) {
            val scope = rememberCoroutineScope()

            BeneficiaryTransactionsScreenContent(
                state = state,
                viewModel = viewModel
            )

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }

    @Composable
    private fun BeneficiaryTransactionsScreenContent(
        modifier: Modifier = Modifier,
        state: ScreenState,
        viewModel: ViewModel
    ) {
        val isDesktop = LocalIsDesktop.current
        var expense by remember { mutableStateOf<ProjectExpense?>(null) }
        val lazyState = rememberLazyListState()
        val table = projectExpenseTable(
            lazyState = lazyState,
            items = state.filteredTransactions,
            isEditEnabled = state.isEditEnabled,
            onEdit = { item -> expense = item }
        )

        AppLazyColumn(
            lazyState = lazyState,
            modifier = modifier.fillMaxSize(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.Start
        ) { horizontalScrollState ->

            item {
                Spacer(modifier = Modifier.sectionsSpacing())
            }

            item {
                var showEditSheet by remember { mutableStateOf(false) }

                AppSurface(
                    modifier = Modifier.shadowBluish(),
                    padding = PaddingValues(Dimensions.Paddings.medium.dp),
                ) {
                    AppColumn(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                        alignment = Alignment.Start
                    ) {
                        AppRow(
                            modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
                            arrangement = Arrangement.SpaceBetween,
                            alignment = Alignment.CenterVertically
                        ) {

                            Box(
                                modifier = Modifier.fillMaxHeight().weight(1f),
                                contentAlignment = Alignment.CenterStart
                            ) {
                                AppSectionTitleText(
                                    state.beneficiary?.name ?: "",
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }

                            if (state.isBeneficiaryDetailsEnabled) {
                                AppTextButton(
                                    modifier = Modifier,
                                    text = "تعديل",
                                    enabled = state.isBeneficiaryDetailsEnabled,
                                    iconPainter = painterResource(Res.drawable.ic_edit),
                                    onClick = { showEditSheet = true }
                                )
                            }
                        }

                        AppLabeledBodyTextHorizontal(label = "الهاتف", text = state.beneficiary?.phoneNumber ?: "")
                    }
                }

                if (showEditSheet) {
                    BeneficiarySheet(
                        beneficiary = state.beneficiary,
                        onDismissRequest = { showEditSheet = false },
                        onFinish = { viewModel.refreshScreen() }
                    )
                }
            }


            item {
                var showEditSheet by remember { mutableStateOf(false) }

                AppRow(
                    modifier = Modifier.fillMaxWidth().sectionsSpacing(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {
                    AppSectionTitleText("الملاحظات", modifier = Modifier.weight(1f))

                    AppTextButton(
                        modifier = Modifier,
                        text = "إضافة",
                        enabled = state.isNotesAddEnabled,
                        onClick = { showEditSheet = true },
                        iconPainter = painterResource(Res.drawable.ic_add)
                    )
                }

                if (showEditSheet) {
                    AddNoteSheet(
                        beneficiary = state.beneficiary,
                        initialNote = null,
                        isEditing = false,
                        onFinishAdd = { viewModel.refreshScreen() },
                        onDismissRequest = { showEditSheet = false }
                    )
                }
            }

            item {
                NotesSection(
                    notes = state.notes,
                    modifier = Modifier,
                    isEditEnabled = state.isNotesAddEnabled,
                    beneficiary = state.beneficiary,
                    onFinishEdit = { viewModel.refreshScreen() }
                )
            }

            item {
                AppSectionTitleText(text = "المشاريع", modifier = Modifier.sectionsSpacing())
            }

            item {
                FiltersSection(
                    choices = state.filters,
                    selected = state.selectedFilter,
                    onClick = { viewModel.filter(it) })
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth().sectionsSpacing(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {

                    AppSectionTitleText(text = "حركة التحويلات", modifier = Modifier.sectionsSpacing())

                    AppTextButton(
                        modifier = Modifier,
                        text = "إستخراج",
                        enabled = state.isEditEnabled,
                        onClick = { viewModel.extractExpenses(state.filteredTransactions) },
                        iconPainter = painterResource(Res.drawable.ic_export),
                        iconMirror = true
                    )
                }
            }


            item { AppSubSectionTitleText("الإجمالي: ${state.totalExpenses.formatted()}") }

            item {
                SearchBar(
                    modifier = Modifier.fillMaxWidth(),
                    onValueChange = { viewModel.search(it) },
                    value = state.search,
                    enabled = true
                )
            }

            BeneficiaryTransactionsListSection(
                table = table,
                scrollState = horizontalScrollState,
                transactions = state.filteredTransactions,
                isPaymentEnabled = false,
                isEditEnabled = state.isEditEnabled,
                isDesktop = isDesktop,
                onRefresh = { viewModel.refreshScreen() }
            )
        }

        if (expense != null) {
            ProjectExpenseSheet(
                project = expense!!.project,
                expense = expense,
                onDismissRequest = { expense = null },
                onFinish = {
                    expense = null
                    viewModel.refreshScreen()
                }
            )
        }
    }
}