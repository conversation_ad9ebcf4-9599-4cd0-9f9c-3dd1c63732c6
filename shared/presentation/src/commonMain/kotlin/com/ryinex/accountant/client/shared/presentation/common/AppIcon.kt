package com.ryinex.accountant.client.shared.presentation.common

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.Icon
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.unit.Dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_eye_hide
import com.ryinex.accountant.client.resources.ic_eye_show
import com.ryinex.accountant.client.shared.presentation.theme.primary
import org.jetbrains.compose.resources.painterResource

sealed interface AppIconDesign {
    val size: Dp
    val padding: Dp
    val shape: RoundedCornerShape
    val tint: AppColor

    data class Default(
        override val size: Dp = DesignSystem.instance.size.icon,
        override val padding: Dp = DesignSystem.instance.padding.icon,
        override val shape: RoundedCornerShape = DesignSystem.instance.shape.icon,
        override val tint: AppColor = primary
    ): AppIconDesign {
        companion object {
            val Default = Default()
        }
    }

    data class Support(
        override val size: Dp = DesignSystem.instance.size.icon,
        override val padding: Dp = DesignSystem.instance.padding.iconSupport,
        override val shape: RoundedCornerShape = DesignSystem.instance.shape.iconSupport,
        override val tint: AppColor = primary
    ): AppIconDesign {
        companion object {
            val Default = Support()
        }
    }
}

@Composable
fun AppIcon(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Default = AppIconDesign.Default.Default
) {
    AppBaseIcon(modifier = modifier, painter = painter, design = design)
}

@Composable
fun AppIconButton(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Default = AppIconDesign.Default.Default,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppBaseIcon(
        modifier = modifier.clickable(enabled = enabled, onClick = onClick),
        painter = painter,
        design = design
    )
}

@Composable
fun AppSupportIcon(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Support = AppIconDesign.Support.Default
) {
    AppBaseIcon(modifier = modifier, painter = painter, design = design)
}

@Composable
fun AppSupportIconButton(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Support = AppIconDesign.Support.Default,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppBaseIcon(
        modifier = modifier.clickable(interactionSource =remember { MutableInteractionSource() }, indication = ripple(), enabled = enabled, onClick = onClick),
        painter = painter,
        design = design
    )
}

@Composable
private fun AppBaseIcon(modifier: Modifier = Modifier, painter: Painter, design: AppIconDesign) {
    Icon(
        modifier = modifier.clip(design.shape).padding(design.padding).size(design.size),
        painter = painter,
        contentDescription = null,
        tint = design.tint.value
    )
}

@Composable
fun AppShowHideIconButton(
    modifier: Modifier = Modifier,
    isShown: Boolean,
    enabled: Boolean = true,
    onClick: () -> Unit
) {
    AppSupportIconButton(
        modifier = modifier,
        painter = if (isShown) painterResource(Res.drawable.ic_eye_hide) else painterResource(Res.drawable.ic_eye_show),
        onClick = onClick,
        enabled = enabled
    )
}