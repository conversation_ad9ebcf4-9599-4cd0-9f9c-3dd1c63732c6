package com.ryinex.accountant.client.shared.presentation.common

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowColumn
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.HorizontalAlignmentLine
import androidx.compose.ui.layout.Measured
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.tables.HorizontalScrollbar
import com.ryinex.accountant.client.shared.presentation.common.tables.VerticalGridScrollbar
import com.ryinex.accountant.client.shared.presentation.common.tables.VerticalScrollbar
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop


@Composable
fun AppColumn(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Vertical,
    alignment: Alignment.Horizontal,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = modifier,
        verticalArrangement = arrangement,
        horizontalAlignment = alignment,
        content = content
    )
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AppFlowColumn(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Vertical,
    alignment: Arrangement.Horizontal,
    maxItems: Int = Int.MAX_VALUE,
    content: @Composable ColumnScope.() -> Unit
) {
    FlowColumn(
        modifier = modifier,
        maxItemsInEachColumn = maxItems,
        verticalArrangement = arrangement,
        horizontalArrangement = alignment,
        content = content
    )
}


@Composable
fun AppLazyColumn(
    modifier: Modifier = Modifier,
    padding: PaddingValues = PaddingValues(0.dp),
    arrangement: Arrangement.Vertical,
    alignment: Alignment.Horizontal,
    lazyState: LazyListState = rememberLazyListState(),
    content: LazyListScope.(horizontalScrollState: ScrollState) -> Unit
) {
    val horizontalScrollState = rememberScrollState()
    Box(modifier = modifier) {
        AppRow(
            arrangement = Arrangement.Start,
            alignment = Alignment.Top
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize().weight(1f),
                verticalArrangement = arrangement,
                horizontalAlignment = alignment,
                contentPadding = padding,
                state = lazyState,
            ) {
                content(horizontalScrollState)
            }

            VerticalScrollbar(
                modifier = Modifier.padding(start = 2.dp),
                state = lazyState
            )
        }

        HorizontalScrollbar(modifier = Modifier.align(Alignment.BottomStart), state = horizontalScrollState)
    }
}


@Composable
fun AppRow(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Horizontal,
    alignment: Alignment.Vertical,
    content: @Composable RowScope.() -> Unit
) {
    Row(
        modifier = modifier,
        horizontalArrangement = arrangement,
        verticalAlignment = alignment,
        content = content
    )
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AppFlowRow(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Vertical,
    alignment: Arrangement.Horizontal,
    maxItems: Int = Int.MAX_VALUE,
    content: @Composable RowScope.() -> Unit
) {
    FlowRow(
        modifier = modifier,
        maxItemsInEachRow = maxItems,
        verticalArrangement = arrangement,
        horizontalArrangement = alignment,
        content = content
    )
}


@Composable
fun AppGrid(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Vertical = Arrangement.spacedBy(Dimensions.Paddings.large.dp),
    alignment: Arrangement.Horizontal = Arrangement.spacedBy(Dimensions.Paddings.large.dp),
    maxItems: Int = if (LocalIsDesktop.current) Dimensions.Cards.maxCardsDesktopCount else 2,
    content: @Composable AppGridScope.() -> Unit
) {
    val density = LocalDensity.current.density
    val maxItems = remember(density) { minOf(maxItems, Dimensions.Cards.maxCardsDesktopCount) }

    AppFlowRow(
        modifier = modifier,
        arrangement = arrangement,
        alignment = alignment,
        maxItems = maxItems,
        content = {
            val scope = remember(this) { AppGridScope(this) }
            content(scope)
        }
    )
}

class AppGridScope(
    private val original: RowScope
) : RowScope {
    override fun Modifier.align(alignment: Alignment.Vertical): Modifier = with(original) {
        <EMAIL>(alignment)
    }

    override fun Modifier.alignBy(alignmentLineBlock: (Measured) -> Int): Modifier = with(original) {
        <EMAIL>(alignmentLineBlock)
    }

    override fun Modifier.alignBy(alignmentLine: HorizontalAlignmentLine): Modifier = with(original) {
        <EMAIL>(alignmentLine)
    }

    override fun Modifier.alignByBaseline(): Modifier = with(original) {
        <EMAIL>()
    }

    override fun Modifier.weight(weight: Float, fill: Boolean): Modifier = with(original) {
        <EMAIL>(weight, fill)
    }

    @Composable
    fun Modifier.adaptWidth(): Modifier = with(original) {
        return if (LocalIsDesktop.current) this@adaptWidth
            .widthIn(min = Dimensions.Cards.maxCardWidthDesktop, max = Dimensions.Cards.maxCardWidthDesktop * 1.5f)
            .weight(1f)
        else <EMAIL>().weight(1f)

    }
}

@Composable
fun Modifier.adaptWidth(): Modifier {
    return if (LocalIsDesktop.current) this.width(Dimensions.Cards.maxCardWidthDesktop)
    else this.fillMaxWidth()
}


@Composable
fun AppLazyGrid(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Vertical = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
    alignment: Arrangement.Horizontal = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Start),
    content: LazyGridScope.() -> Unit
) {
    val state = rememberLazyGridState()
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.Start,
        alignment = Alignment.Top
    ) {
        LazyVerticalGrid(
            verticalArrangement = arrangement,
            horizontalArrangement = alignment,
            content = content,
            state = state,
            columns = if (LocalIsDesktop.current) GridCells.Adaptive(Dimensions.Cards.maxCardWidthDesktop)
            else GridCells.Fixed(1),
        )

        VerticalGridScrollbar(
            modifier = Modifier.padding(start = 2.dp),
            state = state
        )
    }
}

@Composable
fun AppLazyStaggeredGrid(
    modifier: Modifier = Modifier,
    spacing: Dp = Dimensions.Paddings.medium.dp,
    alignment: Arrangement.Horizontal = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Start),
    content: LazyStaggeredGridScope.() -> Unit
) {
    Box(
        modifier = modifier
    ) {
        LazyVerticalStaggeredGrid(
            modifier = Modifier,
            horizontalArrangement = alignment,
            verticalItemSpacing = spacing,
            content = content,
            columns = if (LocalIsDesktop.current) StaggeredGridCells.Adaptive(Dimensions.Cards.maxCardWidthDesktop)
            else StaggeredGridCells.Fixed(1),
        )
    }
}