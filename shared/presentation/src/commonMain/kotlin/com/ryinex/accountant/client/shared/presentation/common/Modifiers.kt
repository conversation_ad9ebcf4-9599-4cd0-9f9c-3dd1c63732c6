package com.ryinex.accountant.client.shared.presentation.common

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawOutline
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.buttons.densityDp
import com.ryinex.accountant.client.shared.presentation.common.containers.nativeShadow

@Composable
fun Modifier.shadow(
    color: Color,
    corner: Dp,
    offsetX: Dp = 1.dp,
    offsetY: Dp = 1.dp,
    radius: Dp = 2.dp,
    direction: LayoutDirection = LocalLayoutDirection.current
) = then(
    padding(
        bottom = offsetY + radius,
        end = if (direction == LayoutDirection.Ltr) offsetX + radius else 0.dp,
        start = if (direction == LayoutDirection.Rtl) offsetX + radius else 0.dp
    ).drawBehind {
        drawIntoCanvas { canvas ->
            val paint = Paint()
            val frameworkPaint = paint.asFrameworkPaint()
            nativeShadow(radius, frameworkPaint, color)

            val leftPixel = offsetX.toPx()
            val topPixel = offsetY.toPx()
            val extraOffset = 2
            val rect = Rect(
                left = if (direction == LayoutDirection.Rtl) leftPixel + extraOffset else size.width + leftPixel + extraOffset,
                top = topPixel + extraOffset,
                right = if (direction == LayoutDirection.Ltr) leftPixel + extraOffset else size.width + leftPixel + extraOffset,
                bottom = size.height + topPixel + extraOffset
            )
            val rounded = RoundRect(rect, CornerRadius(corner.toPx()))

            canvas.drawOutline(Outline.Rounded(rounded), paint)
        }
    }

)

@Composable
fun Modifier.shadowBluish(cornerRadius: Dp = DesignSystem.instance.shape.cardCorner) = shadow(
    color = Color(0xbfB2D4FF), corner = cornerRadius
)

@Composable
fun Modifier.shadowPinkish(cornerRadius: Dp = DesignSystem.instance.shape.cardCorner) = shadow(
    color = Color(0x80C84B7C), corner = cornerRadius
)
