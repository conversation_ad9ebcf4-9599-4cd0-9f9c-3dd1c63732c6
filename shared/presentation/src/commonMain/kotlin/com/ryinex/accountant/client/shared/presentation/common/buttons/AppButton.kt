package com.ryinex.accountant.client.shared.presentation.common.buttons

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dialogs.DelayConfirmDialog
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportIconImageView
import com.ryinex.accountant.client.shared.presentation.common.modifiers.mirror
import com.ryinex.accountant.client.shared.presentation.theme.TajawalFontFamily
import com.ryinex.accountant.client.shared.presentation.theme.on

@Composable
fun AppButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean,
    containerColor: Color = ButtonDefaults.buttonColors().containerColor,
    contentColor: Color = containerColor.on(),
    cornerRadius: Float = Dimensions.Button.cornerRadius,
    prefixContent: (@Composable (Dp) -> Unit)? = null,
    onClick: () -> Unit,
) {
    AppBaseButton(
        modifier = modifier,
        text = text,
        enabled = enabled,
        containerColor = containerColor,
        contentColor = contentColor,
        cornerRadius = cornerRadius,
        prefixContent = prefixContent,
        onClick = onClick
    )
}

@Composable
fun AppDelayedConfirmButton(
    modifier: Modifier = Modifier,
    text: String,
    delayMessage: String = "هل أنت متأكد من أنك تريد هذا الإجراء؟",
    enabled: Boolean,
    containerColor: Color = ButtonDefaults.buttonColors().containerColor,
    contentColor: Color = containerColor.on(),
    cornerRadius: Float = Dimensions.Button.cornerRadius,
    prefixContent: (@Composable (Dp) -> Unit)? = null,
    onClick: () -> Unit,
) {
    var showConfirmDialog by remember { mutableStateOf(false) }

    AppButton(
        modifier = modifier,
        text = text,
        enabled = enabled,
        containerColor = containerColor,
        contentColor = contentColor,
        cornerRadius = cornerRadius,
        prefixContent = prefixContent,
        onClick = { showConfirmDialog = true }
    )

    if (showConfirmDialog) {
        DelayConfirmDialog(
            title = text,
            onDismiss = { showConfirmDialog = false },
            message = delayMessage,
            confirmContainerColor = containerColor,
            confirmTextColor = contentColor,
            onConfirm = {
                onClick()
                showConfirmDialog = false
            }
        )
    }
}

@Composable
fun AppSecondaryButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean,
    containerColor: Color = ButtonDefaults.buttonColors().containerColor,
    contentColor: Color = MaterialTheme.colorScheme.background,
    cornerRadius: Float = Dimensions.Button.cornerRadius,
    onClick: () -> Unit,
) {
    AppBaseButton(
        modifier = modifier,
        text = text,
        enabled = enabled,
        containerColor = containerColor,
        contentColor = contentColor,
        cornerRadius = cornerRadius,
        isSecondary = true,
        onClick = onClick
    )
}

@Composable
fun AppTextButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean,
    color: Color = MaterialTheme.colorScheme.tertiary,
    cornerRadius: Float = Dimensions.Button.cornerRadius,
    iconPainter: Painter? = null,
    iconMirror: Boolean = false,
    onClick: () -> Unit,
) {
    TextButton(
        modifier = modifier.heightIn(min = Dimensions.Button.minHeight.dp),
        enabled = enabled,
        onClick = onClick,
        shape = RoundedCornerShape(cornerRadius),
        colors = ButtonDefaults.textButtonColors(contentColor = color)
    ) {
        AppRow(
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.CenterVertically
        ) {
            if (iconPainter != null) {
                AppSupportIconImageView(
                    modifier = if (iconMirror) Modifier.mirror() else Modifier,
                    painter = iconPainter,
                    iconOnly = true,
                    backgroundColor = if (enabled) color else color.copy(alpha = 0.5f),
                    padding = Dimensions.Paddings.medium,
                    shape = RoundedCornerShape(2.dp),
                    size = 16.dp
                )
            }

            Text(
                text = text,
                fontWeight = FontWeight.Bold,
                fontFamily = TajawalFontFamily
            )
        }
    }
}

@Composable
private fun AppBaseButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean,
    containerColor: Color = ButtonDefaults.buttonColors().containerColor,
    contentColor: Color = ButtonDefaults.buttonColors().contentColor,
    cornerRadius: Float = Dimensions.Button.cornerRadius,
    isSecondary: Boolean = false,
    prefixContent: (@Composable (Dp) -> Unit)? = null,
    onClick: () -> Unit,
) {
    if (!isSecondary) {
        Button(
            modifier = modifier.heightIn(
                min = Dimensions.Button.minHeight.dp
            ),
            enabled = enabled,
            onClick = onClick,
            shape = RoundedCornerShape(cornerRadius),
            colors = ButtonDefaults.buttonColors(containerColor = containerColor, contentColor = contentColor)
        ) {
            var textHeight by remember { mutableStateOf(0) }
            AppRow(
                modifier = Modifier.then(if (textHeight > 0) Modifier.heightIn(max = textHeight.dp) else Modifier),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {

                if (prefixContent != null && textHeight != 0) prefixContent(textHeight.densityDp)

                Text(
                    modifier = Modifier.onGloballyPositioned { textHeight = it.size.height },
                    text = text,
                    fontWeight = FontWeight.Bold,
                    fontFamily = TajawalFontFamily
                )
            }
        }
    } else {
        OutlinedButton(
            modifier = modifier.heightIn(
                min = Dimensions.Button.minHeight.dp
            ),
            enabled = enabled,
            onClick = onClick,
            shape = RoundedCornerShape(cornerRadius),
            colors = ButtonDefaults.outlinedButtonColors(
                containerColor = contentColor,
                contentColor = containerColor
            ),
            border = BorderStroke(
                width = ButtonDefaults.outlinedButtonBorder.width,
                color = containerColor
            )
        ) {
            var textHeight by remember { mutableStateOf(0) }

            AppRow(
                modifier = Modifier.then(if (textHeight > 0) Modifier.heightIn(max = textHeight.dp) else Modifier),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {
                if (prefixContent != null && textHeight != 0) prefixContent(textHeight.densityDp)

                Text(
                    modifier = Modifier.onGloballyPositioned { textHeight = it.size.height },
                    text = text,
                    fontFamily = TajawalFontFamily
                )
            }
        }
    }
}

internal val Int.densityDp @Composable get() = (this / LocalDensity.current.density).dp
