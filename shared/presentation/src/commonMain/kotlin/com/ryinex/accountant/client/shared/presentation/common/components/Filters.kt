package com.ryinex.accountant.client.shared.presentation.common.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.domain.common.utils.FiltersChoice
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppGrid
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop

@Composable
fun FiltersSection(
    modifier: Modifier = Modifier,
    choices: List<FiltersChoice>,
    selected: FiltersChoice,
    onClick: (FiltersChoice) -> Unit
) {
    val isDesktop = LocalIsDesktop.current

    AppSurface(
        modifier = modifier.fillMaxWidth().shadowBluish(),
        padding = PaddingValues(Dimensions.Paddings.medium.dp),
    ) {
        AppGrid(
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            maxItems = if (isDesktop) Dimensions.Cards.maxCardsDesktopCount else 1
        ) {
            for (choice in choices) {
                FiltersSectionItem(
                    modifier = Modifier.adaptWidth(),
                    choice = choice,
                    isSelected = selected.key == choice.key,
                    onClick = onClick
                )
            }
        }
    }
}

@Composable
private fun FiltersSectionItem(
    modifier: Modifier = Modifier,
    choice: FiltersChoice,
    isSelected: Boolean,
    onClick: (FiltersChoice) -> Unit
) {
    AppCard(
        modifier = modifier.fillMaxWidth(),
        padding = Dimensions.Paddings.medium.dp,
        enabled = true,
        onClick = { onClick(choice) },
        regularColors = !isSelected
    ) {
        AppBodyText(
            modifier = Modifier,
            text = choice.title,
        )
    }
}