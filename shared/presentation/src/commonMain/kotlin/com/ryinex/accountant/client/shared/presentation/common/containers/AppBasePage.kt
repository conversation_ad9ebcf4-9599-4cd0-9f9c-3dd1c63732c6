package com.ryinex.accountant.client.shared.presentation.common.containers

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.min
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppOutlinedIconButton
import com.ryinex.accountant.client.shared.presentation.common.pullrefresh.pullRefresh
import com.ryinex.accountant.client.shared.presentation.common.pullrefresh.rememberPullRefreshState
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import core.common.status.Status

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppBasePage(
    title: String,
    isBackEnabled: Boolean = false,
    screenPadding: PaddingValues,
    isPullToRefreshEnabled: Boolean,
    isPullToRefresh: Boolean,
    onPullToRefresh: () -> Unit,
    onBack: () -> Unit = {},
    fab: @Composable () -> Unit = {},
    actions: @Composable RowScope.() -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    val pullRefreshState = rememberPullRefreshState(
        refreshing = isPullToRefresh,
        onRefresh = { onPullToRefresh() }
    )
    val topBarState = rememberTopAppBarState()
    val interactionSource = remember { MutableInteractionSource() }
    val behavior = if (LocalIsDesktop.current) {
        TopAppBarDefaults.pinnedScrollBehavior(topBarState)
    } else {
        TopAppBarDefaults.pinnedScrollBehavior(topBarState)
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .clickable(interactionSource = interactionSource, indication = null) {}) {
        Box(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .fillMaxWidth(if (LocalIsDesktop.current) Dimensions.Desktop.desktopPercentage else 1f)
        ) {
            Scaffold(
                modifier = Modifier
                    .fillMaxSize()
                    .pullRefresh(pullRefreshState, enabled = isPullToRefreshEnabled),
                topBar = {
                    AppRow(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(if (!LocalIsDesktop.current) Dimensions.Paddings.screen.dp else 0.dp),
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.CenterHorizontally),
                        alignment = Alignment.CenterVertically
                    ) {

                        if (isBackEnabled) {
                            AppOutlinedIconButton(
                                painter = rememberVectorPainter(Icons.AutoMirrored.Default.KeyboardArrowLeft),
                                enabled = isBackEnabled,
                                onClick = onBack
                            )
                        }

                        Text(
                            modifier = Modifier.weight(1f),
                            text = title,
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary,
                        )

                        actions()
                    }
                },
                floatingActionButton = fab
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = it.calculateTopPadding())
                        .padding(
                            start = if (!LocalIsDesktop.current) screenPadding.calculateLeftPadding(LocalLayoutDirection.current) else 0.dp,
                            end = if (!LocalIsDesktop.current) screenPadding.calculateRightPadding(LocalLayoutDirection.current) else 0.dp
                        )
                        .nestedScroll(behavior.nestedScrollConnection),
                ) {
                    content()

                    Icon(
                        modifier = Modifier
                            .offset(
                                y = min(
                                    Dimensions.PullToRefresh.size * -1 + Dimensions.PullToRefresh.maxOffset *
                                            pullRefreshState.progress,
                                    Dimensions.PullToRefresh.maxOffset
                                )
                            )
                            .clip(Dimensions.PullToRefresh.shape)
                            .align(Alignment.TopCenter)
                            .size(Dimensions.PullToRefresh.size)
                            .alpha(minOf(1f, pullRefreshState.progress))
                            .background(
                                Dimensions.PullToRefresh.indicatorBackgroundColor,
                                Dimensions.PullToRefresh.shape
                            )
                            .padding(Dimensions.PullToRefresh.indicatorPadding.dp),
                        painter = rememberVectorPainter(image = Icons.Default.Refresh),
                        contentDescription = "تحديث",
                        tint = Dimensions.PullToRefresh.indicatorColor
                    )
                }
            }
        }
    }
}

@Composable
fun BoxScope.AppStatusBar(
    modifier: Modifier = Modifier,
    statuses: List<Status>,
    onCancel: (Status) -> Unit
) {
    com.ryinex.accountant.client.shared.presentation.common.status.StatusBar(
        modifier = modifier
            .padding(bottom = Dimensions.Paddings.screen.dp)
            .then(if (LocalIsDesktop.current) Modifier.widthIn(max = Dimensions.Sections.helperBarMaxWidth.dp) else Modifier.fillMaxWidth())
            .then(if (LocalIsDesktop.current) Modifier.align(Alignment.TopEnd) else Modifier.align(Alignment.BottomCenter)),
        statuses = statuses,
        onCancel = onCancel
    )
}

@Composable
fun ForceLtr(
    enabled: Boolean = true,
    content: @Composable () -> Unit,
) {
    CompositionLocalProvider(LocalLayoutDirection provides if (enabled) LayoutDirection.Ltr else LocalLayoutDirection.current) {
        content()
    }
}


fun Modifier.disableFocus(): Modifier = this
    .focusProperties { this.canFocus = false }