package com.ryinex.accountant.client.shared.presentation.common.containers

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.NativePaint
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.DesignSystem

@Composable
fun AppCard(
    modifier: Modifier = Modifier,
    shape: Shape = DesignSystem.instance.shape.card,
    padding: Dp = 0.dp,
    enabled: Boolean = false,
    regularColors: Boolean = false,
    onClick: () -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    if (enabled) {
        BaseAppCard(
            modifier = modifier,
            shape = shape,
            padding = padding,
            enabled = enabled,
            onClick = onClick,
            content = content,
            regularColors = regularColors
        )
    } else {
        AppCard(
            modifier = modifier,
            shape = shape,
            padding = padding,
            content = content,
            containerColor = CardDefaults.cardColors().containerColor,
        )
    }
}

@Composable
private fun AppCard(
    modifier: Modifier = Modifier,
    shape: Shape = DesignSystem.instance.shape.card,
    padding: Dp = 0.dp,
    containerColor: Color = CardDefaults.cardColors().containerColor,
    contentColor: Color = CardDefaults.cardColors().contentColor,
    content: @Composable BoxScope.() -> Unit
) {
    Card(
        modifier = modifier,
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface ,
            contentColor = MaterialTheme.colorScheme.onSurface
        ),
    ) {
        Box(modifier = Modifier.padding(padding)) { content() }
    }
}

@Composable
private fun BaseAppCard(
    modifier: Modifier = Modifier,
    shape: Shape = DesignSystem.instance.shape.card,
    padding: Dp = 0.dp,
    enabled: Boolean,
    regularColors: Boolean,
    containerColor: Color = MaterialTheme.colorScheme.tertiaryContainer,
    contentColor: Color = MaterialTheme.colorScheme.onTertiaryContainer,
    onClick: () -> Unit,
    content: @Composable BoxScope.() -> Unit
) {
    Card(
        modifier = modifier,
        enabled = enabled,
        onClick = onClick,
        shape = shape,
//        border = CardDefaults.outlinedCardBorder()
//            .copy(
//                width = 2.dp,
//                brush = SolidColor(MaterialTheme.colorScheme.tertiaryContainer)
//            ),
        colors = CardDefaults.cardColors(
            containerColor = if (regularColors) MaterialTheme.colorScheme.surface else containerColor,
            contentColor = if (regularColors) MaterialTheme.colorScheme.onSurface else contentColor
        ),
    ) {
        Box(modifier = Modifier.padding(padding)) { content() }
    }
}

expect fun DrawScope.nativeShadow(
    blurRadius: Dp,
    frameworkPaint: NativePaint,
    color: Color
)