package com.ryinex.accountant.client.shared.presentation.common.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppCircularProgressBar
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import kotlinx.coroutines.delay
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

@Composable
fun DelayConfirmDialog(
    title: String,
    message: String,
    onConfirm: () -> Unit,
    onDismiss: (() -> Unit)? = null,
    confirmText: String = "تأكيد",
    dismissText: String = "إلغاء",
    confirmContainerColor: Color,
    confirmTextColor: Color,
    delay: Duration = 3.seconds
) {
    DelayConfirmDialog(
        title = title,
        onConfirm = onConfirm,
        onDismiss = onDismiss,
        confirmText = confirmText,
        dismissText = dismissText,
        confirmContainerColor = confirmContainerColor,
        confirmTextColor = confirmTextColor,
        delay = delay,
        body = { AppBodyText(message) }
    )
}

@Composable
fun DelayConfirmDialog(
    title: String,
    onConfirm: () -> Unit,
    onDismiss: (() -> Unit)? = null,
    confirmText: String = "تأكيد",
    dismissText: String = "إلغاء",
    confirmContainerColor: Color,
    confirmTextColor: Color,
    delay: Duration = 3.seconds,
    body: @Composable () -> Unit,
) {
    var isEnabled by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        delay(delay)
        isEnabled = true
    }

    Dialog(
        onDismissRequest = onDismiss ?: {},
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Card {
            AppColumn(
                modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                alignment = Alignment.Start
            ) {
                AppSubSectionTitleText(modifier = Modifier, text = title, color = confirmContainerColor)
                body()

                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    if (onDismiss != null) {
                        AppTextButton(
                            text = dismissText,
                            onClick = onDismiss,
                            modifier = Modifier,
                            enabled = true
                        )
                    }

                    AppButton(
                        modifier = Modifier,
                        text = confirmText,
                        containerColor = confirmContainerColor,
                        contentColor = confirmTextColor,
                        enabled = isEnabled,
                        prefixContent = if (!isEnabled) {
                            { AppCircularProgressBar(modifier = Modifier.size(it), color = confirmContainerColor, width = 2.dp) }
                        } else null,
                        onClick = onConfirm
                    )
                }
            }
        }
    }
}