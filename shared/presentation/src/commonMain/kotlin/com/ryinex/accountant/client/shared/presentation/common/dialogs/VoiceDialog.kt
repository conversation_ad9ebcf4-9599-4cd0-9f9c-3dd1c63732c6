package com.ryinex.accountant.client.shared.presentation.common.dialogs

import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_mic
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppIconImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource

@Composable
fun VoiceDialog(
    bestResults: String,
    onFinish: (ByteArray) -> Unit,
    onDismiss: () -> Unit,
) {
    val infiniteTransition = rememberInfiniteTransition()
    val scope = rememberCoroutineScope()
    var isRecording by remember { mutableStateOf(false) }
    val scale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = if (!isRecording) 1f else 0.8f,
        animationSpec = infiniteRepeatable(animation = tween(1000), repeatMode = RepeatMode.Reverse)
    )

    Dialog(
        onDismissRequest = {
            scope.launch { TempDI.voiceRecorder.finish() }
            onDismiss()
        },
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        AppCard(
            padding = Dimensions.Paddings.screen.dp
        ) {
            AppColumn(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                alignment = Alignment.CenterHorizontally
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                    alignment = Alignment.Top
                ) {
                    AppSupportImageViewButton(
                        enabled = true,
                        painter = rememberVectorPainter(Icons.Default.Close),
                        onClick = {
                            scope.launch { TempDI.voiceRecorder.finish() }
                            onDismiss()
                        },
                    )

                    AppBodyText(text = bestResults)
                }

                AppIconImageViewButton(
                    modifier = Modifier.scale(scale),
                    size = Dimensions.IconImage.size * 4,
                    shape = CircleShape,
                    enabled = true,
                    painter = painterResource(Res.drawable.ic_mic),
                    onClick = {
                        scope.launch {
                            if (!isRecording) isRecording = TempDI.voiceRecorder.record()
                            else {
                                isRecording = false
                                onFinish(TempDI.voiceRecorder.finish())
                                onDismiss()
                            }
                        }
                    },
                )

                AppSubSectionTitleText(modifier = Modifier.alpha(if (isRecording) 1f else 0f), text = "تحدث الآن")
            }
        }
    }
}