package com.ryinex.accountant.client.shared.presentation.common.dimensions

import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.theme.onPrimary
import com.ryinex.accountant.client.shared.presentation.theme.primary
import kotlin.math.roundToInt

object Dimensions {

    object Paddings {
        val small = 4f
        val medium = 8f
        val large = 16f
        val screen = 16f
    }

    object Radius {
        val small = 4f
        val medium = 8f
        val large = 24f
        val xLarge = 48f
    }

    object Elevation {
        val small = 2f
        val medium = 4f
        val large = 16f
    }

    object IconImage {
        val size = 40f.dp
    }

    object SupportIconImage {
        val size = 24f.dp
    }

    object RichTextField {
        val minHeight = 60f
    }

    object TextField {
        val minHeight = 20f
        val minWidth = 200f
        val accentColor @Composable get() = MaterialTheme.colorScheme.primary
    }

    object Button {
        val minHeight = 52f
        val cornerRadius = 8f
    }

    object Sections {
        val sideBarMaxWidth = IconImage.size + Paddings.medium.dp * 3
        val helperBarMaxWidth =
            maxOf(TextField.minWidth + Paddings.small, TextField.minHeight * 4 + Paddings.small * 33)
    }

    object HomeScreen {
        val gridItemMinHeight = 100.dp + Paddings.medium.dp * 2
        val gridItemIconSize = 72.dp
        val gridItemIconPadding = 14f
        val gridItemMinWidth = 124.dp
    }

    object Desktop {
        val desktopPercentage = 0.985f
    }

    object Cards {
        val maxCardsDesktopCount = 4
        val maxCardsCountDesktop = ((maxCardsDesktopCount + 0.5) / Desktop.desktopPercentage)
        val maxCardWidthDesktop = (1920 / maxCardsCountDesktop).roundToInt().dp
    }


    object PullToRefresh {
        val indicatorPadding = 4
        val indicatorBackgroundColor @Composable get() = primary.value
        val indicatorColor @Composable get() = onPrimary.value

        val size = 30.dp

        val maxOffset = 100.dp
        val shape = CircleShape
    }
}

object Settings {


    const val confirmDelayInMillis: Long = 1500
}