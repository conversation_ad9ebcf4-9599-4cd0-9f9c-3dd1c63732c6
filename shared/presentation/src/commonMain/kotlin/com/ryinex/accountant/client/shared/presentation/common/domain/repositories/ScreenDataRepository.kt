package com.ryinex.accountant.client.shared.presentation.common.domain.repositories

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class ScreenDataRepository<T>(initial: T, flow: (Flow<T>) -> Flow<T>) {
    private val state: MutableStateFlow<T> = MutableStateFlow(initial)
    val stream: Flow<T> = flow(state).onEach { current = it }
    var current: T = initial
        private set

    fun update(block: (T) -> T) {
        state.update { block(it) }
    }
}