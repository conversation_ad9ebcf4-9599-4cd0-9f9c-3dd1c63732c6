package com.ryinex.accountant.client.shared.presentation.common.feature.components

import androidx.compose.foundation.layout.heightIn
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import com.ryinex.accountant.client.shared.presentation.common.dialogs.DelayConfirmDialog
import com.ryinex.accountant.client.shared.presentation.theme.onPrimary
import com.ryinex.accountant.client.shared.presentation.theme.primary

sealed interface AppButtonDesign {
    val backgroundColor: AppColor
    val contentColor: AppColor
    val shape: Shape
    val minHeight: Dp

    data class Primary(
        override val backgroundColor: AppColor = primary,
        override val contentColor: AppColor = onPrimary
    ) : AppButtonDesign {

        override val shape: Shape = DesignSystem.instance.shape.button
        override val minHeight: Dp = DesignSystem.instance.size.buttonMinHeight

        companion object {
            val Default = Primary()
        }
    }

    data class Text(
        override val contentColor: AppColor = primary
    ) : AppButtonDesign {
        override val backgroundColor: AppColor = AppColor(Color.Transparent)
        override val shape: Shape = DesignSystem.instance.shape.button
        override val minHeight: Dp = DesignSystem.instance.size.buttonMinHeight

        companion object {
            val Default = Text()
        }
    }
}

@Composable
fun AppButton(
    modifier: Modifier = Modifier,
    text: String,
    design: AppButtonDesign = AppButtonDesign.Primary.Default,
    enabled: Boolean,
    onClick: () -> Unit,
) {
    AppBaseButton(modifier = modifier, text = text, design = design, enabled = enabled, onClick = onClick)
}

@Composable
fun AppTextButton(
    modifier: Modifier = Modifier,
    text: String,
    design: AppButtonDesign = AppButtonDesign.Text.Default,
    enabled: Boolean,
    onClick: () -> Unit,
) {
    AppBaseTextButton(modifier = modifier, text = text, design = design, enabled = enabled, onClick = onClick)
}

@Composable
fun AppDelayedConfirmButton(
    modifier: Modifier = Modifier,
    text: String,
    delayMessage: String = "هل أنت متأكد من أنك تريد هذا الإجراء؟",
    design: AppButtonDesign = AppButtonDesign.Primary.Default,
    enabled: Boolean,
    onClick: () -> Unit,
) {
    var showConfirmDialog by remember { mutableStateOf(false) }

    AppButton(
        modifier = modifier,
        text = text,
        design = design,
        enabled = enabled,
        onClick = { showConfirmDialog = true }
    )

    if (showConfirmDialog) {
        DelayConfirmDialog(
            title = text,
            onDismiss = { showConfirmDialog = false },
            message = delayMessage,
            confirmContainerColor = design.backgroundColor.value,
            confirmTextColor = design.contentColor.value,
            onConfirm = {
                onClick()
                showConfirmDialog = false
            }
        )
    }
}

@Composable
private fun AppBaseButton(
    modifier: Modifier = Modifier,
    text: String,
    design: AppButtonDesign,
    enabled: Boolean,
    onClick: () -> Unit,
) {
    Button(
        modifier = modifier.heightIn(min = design.minHeight),
        enabled = enabled,
        onClick = onClick,
        shape = design.shape,
        colors = ButtonDefaults.buttonColors(
            containerColor = design.backgroundColor.value,
            contentColor = design.contentColor.value
        ),
        content = { AppBodyText(text = text) }
    )
}

@Composable
private fun AppBaseTextButton(
    modifier: Modifier = Modifier,
    text: String,
    design: AppButtonDesign,
    enabled: Boolean,
    onClick: () -> Unit,
) {
    TextButton(
        modifier = modifier.heightIn(min = design.minHeight),
        enabled = enabled,
        onClick = onClick,
        shape = design.shape,
        colors = ButtonDefaults.textButtonColors(
            contentColor = design.contentColor.value
        ),
        content = { AppBodyText(text = text) }
    )
}