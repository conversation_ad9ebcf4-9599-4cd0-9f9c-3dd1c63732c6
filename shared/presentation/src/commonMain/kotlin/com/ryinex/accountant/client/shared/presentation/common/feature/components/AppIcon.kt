package com.ryinex.accountant.client.shared.presentation.common.feature.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.unit.Dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_eye_hide
import com.ryinex.accountant.client.resources.ic_eye_show
import com.ryinex.accountant.client.shared.presentation.theme.primary
import com.ryinex.accountant.shared.presentation.ConfigValues
import org.jetbrains.compose.resources.painterResource

sealed interface AppIconDesign {
    val size: Dp
    val padding: Dp
    val shape: RoundedCornerShape
    val tint: AppColor

    data class Default(
        override val size: Dp = DesignSystem.instance.size.icon,
        override val padding: Dp = DesignSystem.instance.padding.icon,
        override val shape: RoundedCornerShape = DesignSystem.instance.shape.icon,
        override val tint: AppColor = primary
    ) : AppIconDesign {
        companion object {
            val Default = Default()
        }
    }

    data class Support(
        override val size: Dp = DesignSystem.instance.size.iconSupport,
        override val padding: Dp = DesignSystem.instance.padding.iconSupport,
        override val shape: RoundedCornerShape = DesignSystem.instance.shape.iconSupport,
        override val tint: AppColor = primary
    ) : AppIconDesign {
        companion object {
            val Default = Support()
        }
    }
}

@Composable
fun AppIcon(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Default = AppIconDesign.Default.Default
) {
    AppBaseIcon(modifier = modifier, painter = painter, design = design)
}

@Composable
fun AppIconButton(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Default = AppIconDesign.Default.Default,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppBaseIcon(modifier = modifier.clickable(enabled = enabled, onClick = onClick), painter = painter, design = design)
}

@Composable
fun AppSupportIcon(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Support = AppIconDesign.Support.Default
) {
    AppBaseIcon(modifier = modifier, painter = painter, design = design)
}

@Composable
fun AppSupportIconButton(
    modifier: Modifier = Modifier,
    painter: Painter,
    design: AppIconDesign.Support = AppIconDesign.Support.Default,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppBaseIcon(
        modifier = modifier.clickable(enabled = enabled, onClick = onClick), painter = painter, design = design
    )
}

@Composable
private fun AppBaseIcon(modifier: Modifier = Modifier, painter: Painter, design: AppIconDesign) {
    Icon(
        modifier = Modifier.clip(design.shape).padding(design.padding).size(design.size).then(modifier),
        painter = painter,
        contentDescription = null,
        tint = design.tint.value
    )
}

@Composable
fun AppShowHideIconButton(
    modifier: Modifier = Modifier,
    isShown: Boolean,
    enabled: Boolean = true,
    onClick: () -> Unit
) {
    AppSupportIconButton(
        modifier = modifier,
        painter = if (isShown) painterResource(Res.drawable.ic_eye_hide) else painterResource(Res.drawable.ic_eye_show),
        onClick = onClick,
        enabled = enabled
    )
}

@Composable
fun AppCircularProgressBar(modifier: Modifier = Modifier, color: Color, width: Dp) {
    if (ConfigValues.environment != "dev") {
        CircularProgressIndicator(
            modifier = modifier,
            color = color,
            strokeWidth = width
        )
    }
}