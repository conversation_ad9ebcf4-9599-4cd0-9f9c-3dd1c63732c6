package com.ryinex.accountant.client.shared.presentation.common.feature.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.ryinex.accountant.client.shared.presentation.theme.tertiary


sealed interface AppTextDesign {
    val color: AppColor
    val weight: FontWeight
    val size: TextUnit
    val alignment: TextAlign
    val lineHeight: TextUnit
    val textDecoration: TextDecoration
    val minLines: Int
    val overflow: TextOverflow

    data class Body(
        override val color: AppColor,
        override val alignment: TextAlign = TextAlign.Start,
        override val weight: FontWeight = DesignSystem.instance.fontWeight.body,
        override val textDecoration: TextDecoration = TextDecoration.None,
        override val minLines: Int = 1,
        override val overflow: TextOverflow = TextOverflow.Clip
    ) : AppTextDesign {
        override val size: TextUnit = DesignSystem.instance.fontSize.body
        override val lineHeight: TextUnit = TextUnit.Unspecified

        companion object {
            val Default
                @Composable get() = Body(AppColor(LocalContentColor.current))
        }
    }

    data class Label(
        override val color: AppColor,
        override val size: TextUnit = DesignSystem.instance.fontSize.label,
        override val alignment: TextAlign = TextAlign.Start,
        override val textDecoration: TextDecoration = TextDecoration.None,
        override val overflow: TextOverflow = TextOverflow.Clip,
        override val minLines: Int = 1,
        override val weight: FontWeight = DesignSystem.instance.fontWeight.label
    ) : AppTextDesign {
        override val lineHeight: TextUnit = (size.value + 2).sp

        companion object {
            val Default
                @Composable get() = Label(AppColor(LocalContentColor.current))
        }
    }

    data class SectionTitle(
        override val color: AppColor,
        override val alignment: TextAlign = TextAlign.Start,
        override val textDecoration: TextDecoration = TextDecoration.None,
        override val overflow: TextOverflow = TextOverflow.Clip
    ) : AppTextDesign {
        override val minLines: Int = 1
        override val lineHeight: TextUnit = TextUnit.Unspecified
        override val weight: FontWeight = DesignSystem.instance.fontWeight.title
        override val size: TextUnit = DesignSystem.instance.fontSize.title

        companion object {
            val Default @Composable get() = SectionTitle(tertiary)
        }
    }

    data class ScreenTitle(
        override val color: AppColor,
        override val alignment: TextAlign = TextAlign.Start,
        override val textDecoration: TextDecoration = TextDecoration.None,
        override val overflow: TextOverflow = TextOverflow.Clip
    ) : AppTextDesign {
        override val minLines: Int = 1
        override val lineHeight: TextUnit = TextUnit.Unspecified
        override val weight: FontWeight = DesignSystem.instance.fontWeight.screenTitle
        override val size: TextUnit = DesignSystem.instance.fontSize.screenTitle

        companion object {
            val Default @Composable get() = ScreenTitle(AppColor(LocalContentColor.current))
        }
    }
}

@Composable
fun AppVerticalLabeledBodyText(
    text: String,
    label: String,
    modifier: Modifier = Modifier,
    textDesign: AppTextDesign.Body = AppTextDesign.Body.Default,
    labelDesign: AppTextDesign.Label = AppTextDesign.Label.Default,
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.instance.padding.minimum),
        alignment = Alignment.Start
    ) {
        AppLabelText(label, design = labelDesign)
        AppBodyText(text, design = textDesign)
    }
}

@Composable
fun AppBodyText(
    text: String,
    modifier: Modifier = Modifier,
    design: AppTextDesign.Body = AppTextDesign.Body.Default
) {
    AppBaseText(text, modifier, design)
}

@Composable
fun AppLabelText(
    text: String,
    modifier: Modifier = Modifier,
    design: AppTextDesign.Label = AppTextDesign.Label.Default
) {
    AppBaseText(text, modifier, design)
}

@Composable
fun AppSectionTitleText(
    text: String,
    modifier: Modifier = Modifier,
    design: AppTextDesign.SectionTitle = AppTextDesign.SectionTitle.Default
) {
    AppBaseText(text, modifier, design)
}

@Composable
fun AppScreenTitleText(
    text: String,
    modifier: Modifier = Modifier,
    design: AppTextDesign.ScreenTitle = AppTextDesign.ScreenTitle.Default
) {
    AppBaseText(text, modifier, design)
}

@Composable
private fun AppBaseText(text: String, modifier: Modifier = Modifier, design: AppTextDesign) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = design.size,
        lineHeight = design.lineHeight,
        color = design.color.value,
        fontWeight = design.weight,
        textAlign = design.alignment,
        textDecoration = design.textDecoration,
        minLines = design.minLines,
        overflow = design.overflow
    )
}