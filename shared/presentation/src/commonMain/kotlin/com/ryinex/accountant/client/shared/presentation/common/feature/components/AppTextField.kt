package com.ryinex.accountant.client.shared.presentation.common.feature.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.isShiftPressed
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.Dp
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.ItemState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.theme.background
import com.ryinex.accountant.client.shared.presentation.theme.onBackground
import kotlinx.coroutines.flow.Flow

data class AppTextFieldState(val text: StringState, val error: ItemState<Boolean>) {
    constructor(text: StringState = StringState(""), error: (Flow<String>) -> Flow<Boolean>) : this(
        text, DerivedState(false, error(text.stream))
    )
}

sealed interface AppTextFieldDesign {
    val backgroundColor: AppColor
    val contentColor: AppColor
    val shape: Shape
    val minHeight: Dp
    val minWidth: Dp
    val keyboardOptions: KeyboardOptions
    val keyboardActions: KeyboardActions
    val singleLine: Boolean

    data class NormalTextField(
        override val backgroundColor: AppColor = background,
        override val contentColor: AppColor = onBackground,
        override val singleLine: Boolean = false,
        override val keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
        override val keyboardActions: KeyboardActions = KeyboardActions.Default,
        override val minWidth: Dp = DesignSystem.instance.size.textFieldMinWidth
    ) : AppTextFieldDesign {
        override val shape: RoundedCornerShape = DesignSystem.instance.shape.textField
        override val minHeight: Dp = DesignSystem.instance.size.textFieldMinHeight

        companion object {
            val Default = NormalTextField()
        }
    }
}

@Composable
fun AppPasswordTextField(
    modifier: Modifier = Modifier,
    state: AppTextFieldState,
    hint: String,
    enabled: Boolean,
    design: AppTextFieldDesign = AppTextFieldDesign.NormalTextField.Default,
    helperText: String = "",
    errorText: String = ""
) {
    var isShown by remember { mutableStateOf(false) }
    AppBaseTextField(
        state = state,
        modifier = modifier,
        hint = hint,
        enabled = enabled,
        design = design,
        helperText = helperText,
        errorText = errorText,
        visualTransformation = if (isShown) VisualTransformation.None else PasswordVisualTransformation(),
        trailing = { AppShowHideIconButton(isShown = isShown, onClick = { isShown = !isShown }) }
    )
}

@Composable
fun AppTextField(
    modifier: Modifier = Modifier,
    state: AppTextFieldState,
    hint: String,
    enabled: Boolean,
    design: AppTextFieldDesign = AppTextFieldDesign.NormalTextField.Default,
    helperText: String = "",
    errorText: String = ""
) {
    AppBaseTextField(
        state = state,
        modifier = modifier,
        hint = hint,
        enabled = enabled,
        design = design,
        helperText = helperText,
        errorText = errorText
    )
}

@Composable
private fun AppBaseTextField(
    state: AppTextFieldState,
    modifier: Modifier = Modifier,
    hint: String,
    enabled: Boolean,
    design: AppTextFieldDesign = AppTextFieldDesign.NormalTextField.Default,
    helperText: String = "",
    errorText: String = "",
    visualTransformation: VisualTransformation = VisualTransformation.None,
    trailing: (@Composable () -> Unit)? = null
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.instance.padding.minimum),
        alignment = Alignment.Start
    ) {
        OutlinedTextField(
            modifier = modifier.heightIn(min = design.minHeight).widthIn(min = design.minWidth).moveFocusOnTab().moveFocusOnEnterSingleLine(design.singleLine),
            value = state.text.listen,
            onValueChange = state.text::update,
            label = { AppBodyText(text = hint) },
            enabled = enabled,
            shape = design.shape,
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = design.backgroundColor.value,
                unfocusedContainerColor = design.backgroundColor.value,
                focusedTextColor = design.contentColor.value,
                unfocusedTextColor = design.contentColor.value
            ),
            keyboardActions = design.keyboardActions,
            keyboardOptions = design.keyboardOptions,
            singleLine = design.singleLine,
            trailingIcon = trailing,
            visualTransformation = visualTransformation
        )

        if (helperText.isNotBlank()) AppLabelText(text = helperText)
        if (state.error.listen) AppLabelText(
            text = errorText,
            design = AppTextDesign.Label.Default.copy(color = com.ryinex.accountant.client.shared.presentation.theme.error)
        )
    }
}


@OptIn(ExperimentalComposeUiApi::class)
fun Modifier.moveFocusOnTab() = composed {
    val focusManager = LocalFocusManager.current
    onPreviewKeyEvent {
        if (it.type == KeyEventType.KeyDown && it.key == Key.Tab) {
            focusManager.moveFocus(if (it.isShiftPressed) FocusDirection.Previous else FocusDirection.Next)
            true
        } else {
            false
        }
    }
}

fun Modifier.moveFocusOnEnterSingleLine(enabled: Boolean) = composed {
    val focusManager = LocalFocusManager.current
    onPreviewKeyEvent {
        if (enabled && it.type == KeyEventType.KeyDown && ( it.key == Key.Enter || it.key == Key.NumPadEnter)) {
            focusManager.moveFocus(FocusDirection.Next)
            true
        } else {
            false
        }
    }
}