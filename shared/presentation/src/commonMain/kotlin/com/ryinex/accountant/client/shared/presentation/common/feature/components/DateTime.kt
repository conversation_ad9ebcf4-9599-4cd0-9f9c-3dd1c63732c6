package com.ryinex.accountant.client.shared.presentation.common.feature.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import dev.darkokoa.datetimewheelpicker.WheelDatePicker
import dev.darkokoa.datetimewheelpicker.WheelTimePicker
import dev.darkokoa.datetimewheelpicker.core.format.MonthDisplayStyle
import dev.darkokoa.datetimewheelpicker.core.format.TimeFormat
import dev.darkokoa.datetimewheelpicker.core.format.dateFormatter
import dev.darkokoa.datetimewheelpicker.core.format.timeFormatter
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime

@Composable
fun DateTimePicker(modifier: Modifier = Modifier, now: LocalDateTime, onPicked: (LocalDateTime) -> Unit) {
    var ignoreCounter by remember { mutableStateOf(0) }
    var currentDate by remember { mutableStateOf(now.date) }
    var currentTime by remember { mutableStateOf(now.time) }
    val nowDate = remember { now }

    LaunchedEffect(currentDate, currentTime) {
        if (ignoreCounter < 2) {
            ignoreCounter++
            return@LaunchedEffect
        }
        onPicked(LocalDateTime(currentDate, currentTime))
    }

    AppRow(
        modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.CenterHorizontally),
        alignment = Alignment.CenterVertically
    ) {
        WheelDatePicker(
            modifier = Modifier.weight(1f),
            dateFormatter = dateFormatter(locale = Locale("ar"), monthDisplayStyle = MonthDisplayStyle.FULL),
            startDate = currentDate,
            minDate = remember { LocalDate(nowDate.date.year - 1, 1, 1) },
            maxDate = remember { LocalDate(nowDate.date.year, 12, 31) },
            onSnappedDate = { currentDate = it }
        )

        WheelTimePicker(
            startTime = currentTime,
            timeFormatter = timeFormatter(locale = Locale("ar"), TimeFormat.AM_PM),
            onSnappedTime = { currentTime = it }
        )
    }
}
