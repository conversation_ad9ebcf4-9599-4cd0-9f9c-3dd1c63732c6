package com.ryinex.accountant.client.shared.presentation.common.feature.components

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

class AppColor(private val light: Color, private val dark: Color = light) {
    val value: Color
        @Composable get() {
            return light
            val darkTheme = isSystemInDarkTheme()
            return remember(darkTheme) { if (darkTheme) dark else light }
        }
}

class DesignSystem {
    val fontSize: FontSize = FontSize()
    val corner = Corner()
    val size = Size()
    val shape = Shape()
    val fontWeight = FontWeight()
    val padding = Padding()

    class Padding {
        val minimum = 2.dp
        val icon = 4.dp
        val iconSupport = 2.dp
    }

    class Corner {
        val textField = 8.dp
    }

    class Size {
        val buttonMinHeight = 52.dp
        val textFieldMinHeight = 52.dp
        val textFieldMinWidth = 200.dp
        val icon = 24.dp
        val iconSupport = 16.dp
    }

    class Shape {
        val button = RoundedCornerShape(8.dp)
        val textField = RoundedCornerShape(8.dp)
        val icon = CircleShape
        val iconSupport = CircleShape
        val cardCorner = 16.dp
        val card = RoundedCornerShape(cardCorner)
    }

    class FontWeight {
        val label = androidx.compose.ui.text.font.FontWeight.Bold
        val body = androidx.compose.ui.text.font.FontWeight.Medium
        val title = androidx.compose.ui.text.font.FontWeight.Bold
        val screenTitle = androidx.compose.ui.text.font.FontWeight.Bold
    }

    class FontSize {
        val label = 12.sp
        val body = 16.sp // Reference (support icon size, ....)
        val title = 20.sp
        val screenTitle = 24.sp
    }

    companion object {
        val instance = DesignSystem()
    }
}
