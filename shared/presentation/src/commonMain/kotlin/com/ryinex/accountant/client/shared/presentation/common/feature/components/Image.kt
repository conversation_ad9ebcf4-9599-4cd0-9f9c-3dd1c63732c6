package com.ryinex.accountant.client.shared.presentation.common.feature.components

import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource

@Composable
fun AppImage(
    modifier: Modifier = Modifier,
    res: DrawableResource,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Fit
) {
    Image(
        modifier = modifier,
        painter = painterResource(res),
        contentDescription = contentDescription,
        contentScale = contentScale
    )
}