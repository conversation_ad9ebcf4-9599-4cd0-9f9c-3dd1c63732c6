package com.ryinex.accountant.client.shared.presentation.common.selection

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText

@Composable
fun TitledSelectedItem(
    modifier: Modifier = Modifier,
    title: String,
    text: String,
    cancelEnabled: Boolean = true,
    onCancel: () -> Unit
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.Top,
        alignment = Alignment.Start
    ) {
        AppLabelText(text = title, color = Dimensions.TextField.accentColor, fontWeight = FontWeight.Bold)

        SelectedItem(
            modifier = modifier,
            text = text,
            cancelEnabled = cancelEnabled,
            onCancel = onCancel
        )
    }
}

@Composable
fun SelectedItem(
    modifier: Modifier = Modifier,
    text: String,
    cancelEnabled: Boolean = true,
    onCancel: () -> Unit
) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.CenterVertically
    ) {
        AppSubSectionTitleText(
            text = text,
            fontWeight = FontWeight.Bold
        )

        if (cancelEnabled) {
            AppSupportImageViewButton(
                painter = rememberVectorPainter(Icons.Default.Close),
                enabled = cancelEnabled,
                onClick = onCancel
            )
        }
    }
}