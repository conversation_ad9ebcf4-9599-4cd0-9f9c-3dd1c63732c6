package com.ryinex.accountant.client.shared.presentation.common.status


import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppCircularProgressBar
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.language.show
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.theme.AppTheme
import com.ryinex.accountant.client.shared.presentation.theme.LocalLocale
import com.ryinex.accountant.client.shared.presentation.theme.on
import com.ryinex.accountant.client.shared.presentation.theme.tertiaryContainer
import core.common.message.Message
import core.common.status.Status
import org.jetbrains.compose.ui.tooling.preview.Preview
import core.common.error.Error

@Composable
fun StatusBar(
    modifier: Modifier = Modifier,
    statuses: List<Status>,
    onCancel: (Status) -> Unit
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.Start
    ) {
        statuses.forEachIndexed { index, item ->
            when (item) {
                is Status.Info -> Info(
                    modifier = getPadding(index),
                    status = item,
                    onClick = onCancel
                )

                is Status.Loading -> Loading(
                    modifier = getPadding(index),
                    status = item,
                    onClick = onCancel
                )

                is Status.Fail -> Error(
                    modifier = getPadding(index),
                    status = item,
                    onClick = onCancel
                )

                is Status.Success -> Success(
                    modifier = getPadding(index),
                    status = item,
                    onClick = onCancel
                )

                is Status.Intermediate -> Intermediate(
                    modifier = getPadding(index),
                    status = item,
                    onClick = onCancel
                )
            }
        }
    }
}

private fun getPadding(index: Int): Modifier {
    // return Modifier.offset(x = 2.5.dp * index, y = 2.5.dp * index)
    return Modifier
}

@Composable
private fun Intermediate(modifier: Modifier, status: Status, onClick: (Status) -> Unit) {
    CommonStatusBar(
        modifier = modifier,
        text = status.message.show(LocalLocale.current),
        backgroundColor = MaterialTheme.colorScheme.primary,
        endWidget = {
            AppCircularProgressBar(
                modifier = Modifier.size(20.dp),
                width = 2.dp,
                color = it
            )
        },
        onClick = { onClick(status) }
    )
}

@Composable
private fun Info(modifier: Modifier, status: Status, onClick: (Status) -> Unit) {
    CommonStatusBar(
        modifier = modifier,
        text = status.message.show(LocalLocale.current),
        backgroundColor = MaterialTheme.colorScheme.primary,
        endWidget = {},
        onClick = { onClick(status) }
    )
}

@Composable
private fun Loading(modifier: Modifier, status: Status, onClick: (Status) -> Unit) {
    CommonStatusBar(
        modifier = modifier,
        text = status.message.show(LocalLocale.current),
        backgroundColor = MaterialTheme.colorScheme.primary,
        endWidget = {
            AppCircularProgressBar(
                modifier = Modifier.size(20.dp),
                width = 2.dp,
                color = it
            )
        },
        onClick = { onClick(status) }
    )
}

@Composable
private fun Success(modifier: Modifier, status: Status, onClick: (Status) -> Unit) {
    CommonStatusBar(
        modifier = modifier,
        text = status.message.show(LocalLocale.current),
        backgroundColor = tertiaryContainer.value,
        endWidget = { },
        onClick = { onClick(status) }
    )
}

@Composable
private fun Error(modifier: Modifier, status: Status.Fail, onClick: (Status) -> Unit) {
    val uid = if (status.error is Error.Fatal) (status.error as Error.Fatal).errorUid else ""
    CommonStatusBar(
        modifier = modifier,
        text = "${status.message.show(LocalLocale.current)}\n${
            if (uid.isNotBlank()) "Issue Id: $uid" else ""
        }".trim(),
        backgroundColor = MaterialTheme.colorScheme.error,
        endWidget = {
            Icon(
                modifier = Modifier
                    .clip(CircleShape)
                    .clickable {
                        onClick(status)
                    },
                imageVector = Icons.Default.Close,
                contentDescription = "",
                tint = it
            )
        },
        onClick = { onClick(status) }
    )
}

@Composable
private fun CommonStatusBar(
    modifier: Modifier,
    text: String,
    backgroundColor: Color,
    endWidget: @Composable (Color) -> Unit,
    onClick: () -> Unit
) {
    AppSurface(
        modifier = modifier,
        color = backgroundColor
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onClick() }
                .padding(Dimensions.Paddings.medium.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            AppBodyText(
                modifier = Modifier.weight(1f),
                text = text,
                color = backgroundColor.on()
            )

            endWidget(backgroundColor.on())
        }
    }
}

@Composable
internal fun StatusBarPreviewSample() {
    StatusBar(
        statuses = listOf(
            Status.Info(
                Message(
                    en = "Loading...",
                    ar = "جاري التحميل..."
                )
            ),
            Status.Fail(
                Error.Fatal(
                    message = "",
                    callLocation = "callSite",
                    details = mapOf()
                )
            ),
            Status.Loading(
                Message(
                    en = "Loading...",
                    ar = "جاري التحميل..."
                )
            ),
            Status.Success(
                Message(
                    en = "Loading...",
                    ar = "جاري التحميل..."
                )
            ),
            Status.Intermediate(
                Message(
                    en = "Loading...",
                    ar = "جاري التحميل..."
                )
            )
        ),
        onCancel = {}
    )
}

@Preview
@Composable
private fun StatusBarPreview() {
    AppTheme { StatusBarPreviewSample() }
}
