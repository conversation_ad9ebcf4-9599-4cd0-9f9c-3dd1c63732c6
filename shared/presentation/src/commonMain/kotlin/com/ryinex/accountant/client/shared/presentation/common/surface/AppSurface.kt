package com.ryinex.accountant.client.shared.presentation.common.surface

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.DesignSystem
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.theme.on

@Composable
fun AppSurface(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.surface,
    onColor: Color = MaterialTheme.colorScheme.onSurface,
    elevation: Float = Dimensions.Elevation.small,
    shape: Shape = DesignSystem.instance.shape.card,
    padding: PaddingValues = PaddingValues(0.dp),
    clickable: Boolean = false,
    onClick: () -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    Surface(
        modifier = modifier.clip(shape),
        color = color,
        contentColor = onColor,
        shadowElevation = 1.dp,
        tonalElevation = elevation.dp,
        shape = shape
    ) {
        Box(Modifier.customClickable(enabled = clickable, onClick = onClick).padding(padding)) {
            content()
        }
    }
}

@Composable
fun AppSelectableSurface(
    modifier: Modifier = Modifier,
    isSelected: Boolean,
    color: Color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surface,
    onColor: Color = color.on(),
    elevation: Float = Dimensions.Elevation.small,
    padding: PaddingValues = PaddingValues(0.dp),
    clickable: Boolean = false,
    onClick: () -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    AppSurface(
        modifier = modifier,
        color = color,
        onColor = onColor,
        elevation = elevation,
        padding = padding,
        clickable = clickable,
        onClick = onClick,
        content = content
    )
}

@Composable
fun AppIconTextSurfaceVertical(
    modifier: Modifier = Modifier,
    text: String,
    painter: Painter,
    enabled: Boolean,
    isSelected: Boolean,
    cornerRadius: Float = Dimensions.Button.cornerRadius,
    onClick: () -> Unit,
) {
    AppSelectableSurface(
        modifier = modifier
            .clickable(enabled = enabled, onClick = onClick),
        padding = PaddingValues(Dimensions.Paddings.small.dp),
        isSelected = isSelected,
        elevation = Dimensions.Paddings.large,
    ) {
        AppColumn(
            modifier = Modifier
                .padding(horizontal = Dimensions.Paddings.small.dp)
                .widthIn(min = Dimensions.TextField.minWidth.dp * 1 / 3),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.CenterVertically),
            alignment = Alignment.CenterHorizontally
        ) {
            Icon(
                modifier = Modifier.size(Dimensions.Button.minHeight.dp / 2),
                painter = painter,
                contentDescription = text,
            )

            AppBodyText(text = text)
        }
    }
}


@Composable
private fun Modifier.customClickable(
    enabled: Boolean,
    onClick: () -> Unit
) = if (enabled) this.clickable(onClick = onClick) else this
