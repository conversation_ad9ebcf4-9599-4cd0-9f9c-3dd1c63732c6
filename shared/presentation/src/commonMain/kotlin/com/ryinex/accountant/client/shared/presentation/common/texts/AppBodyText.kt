package com.ryinex.accountant.client.shared.presentation.common.texts


import androidx.compose.foundation.layout.Arrangement
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions

@Composable
fun AppBodyText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = LocalContentColor.current,
    fontFamily: FontFamily? = MaterialTheme.typography.bodyMedium.fontFamily,
    fontWeight: FontWeight? = FontWeight.SemiBold,
    fontSize: Float = MaterialTheme.typography.bodyMedium.fontSize.value,
    overflow: TextOverflow = TextOverflow.Clip,
    textAlign: TextAlign? = null,
    minLines: Int = 1,
    maxLines: Int = Int.MAX_VALUE
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize.sp,
        lineHeight = (fontSize + 4).sp,
        color = color,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        overflow = overflow,
        textAlign = textAlign,
        minLines = minLines,
        maxLines = maxLines
    )
}

@Composable
fun AppTitleText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = LocalContentColor.current,
    fontFamily: FontFamily? = MaterialTheme.typography.titleMedium.fontFamily,
    fontWeight: FontWeight? = MaterialTheme.typography.titleMedium.fontWeight,
    fontSize: Float = MaterialTheme.typography.titleMedium.fontSize.value,
    overflow: TextOverflow = TextOverflow.Clip,
    textAlign: TextAlign? = null
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize.sp,
        lineHeight = (fontSize + 4).sp,
        color = color,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        overflow = overflow,
        textAlign = textAlign
    )
}

@Composable
fun AppLabelText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.tertiary,
    fontFamily: FontFamily? = MaterialTheme.typography.bodySmall.fontFamily,
    fontWeight: FontWeight? = FontWeight.Bold,
    fontSize: Float = MaterialTheme.typography.bodySmall.fontSize.value,
    overflow: TextOverflow = TextOverflow.Clip
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize.sp,
        lineHeight = (fontSize + 4).sp,
        color = color,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        overflow = overflow
    )
}

@Composable
fun AppLabelText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.tertiary,
    fontFamily: FontFamily? = MaterialTheme.typography.bodySmall.fontFamily,
    fontWeight: FontWeight? = FontWeight.Bold,
    fontSize: Float = MaterialTheme.typography.bodySmall.fontSize.value,
    overflow: TextOverflow = TextOverflow.Clip
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize.sp,
        lineHeight = (fontSize + 4).sp,
        color = color,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        overflow = overflow
    )
}

@Composable
fun AppSectionTitleText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = LocalContentColor.current,
    fontFamily: FontFamily? = MaterialTheme.typography.headlineMedium.fontFamily,
    fontWeight: FontWeight? = FontWeight.Bold,
    fontSize: Float = MaterialTheme.typography.headlineMedium.fontSize.value,
    overflow: TextOverflow = TextOverflow.Clip
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize.sp,
        lineHeight = (fontSize + 4).sp,
        color = color,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        overflow = overflow
    )
}

@Composable
fun AppSubSectionTitleText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.tertiary,
    fontFamily: FontFamily? = MaterialTheme.typography.titleLarge.fontFamily,
    fontWeight: FontWeight? = MaterialTheme.typography.titleLarge.fontWeight,
    fontSize: Float = MaterialTheme.typography.titleLarge.fontSize.value,
    overflow: TextOverflow = TextOverflow.Clip
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize.sp,
        lineHeight = (fontSize + 4).sp,
        color = color,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        overflow = overflow
    )
}

@Composable
fun AppPageTitleText(
    text: String,
    modifier: Modifier = Modifier,
    fontSize: Float = MaterialTheme.typography.headlineLarge.fontSize.value,
    color: Color = LocalContentColor.current
) {
    AppBodyText(
        modifier = modifier,
        text = text,
        fontSize = fontSize,
        color = color,
    )
}


@Composable
fun AppLabeledTextVertical(
    modifier: Modifier = Modifier,
    label: String,
    text: String,
    alignment: Alignment.Horizontal = Alignment.Start,
    color: Color = LocalContentColor.current,
    labelColor: Color = LocalContentColor.current,
    fontWeight: FontWeight? = MaterialTheme.typography.bodyMedium.fontWeight,
    fontSize: Float = MaterialTheme.typography.bodyMedium.fontSize.value,
    spacing: Float = Dimensions.Paddings.small,
    ltrText: Boolean = false
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(spacing.dp),
        alignment = alignment
    ) {
        BaseAppLabeledBodyText(
            label = label,
            text = text,
            color = color,
            labelColor = labelColor,
            ltrText = ltrText
        )
    }
}

@Composable
fun AppLabeledBodyTextHorizontal(
    modifier: Modifier = Modifier,
    label: String,
    text: String,
    alignment: Alignment.Vertical = Alignment.Top,
    color: Color = LocalContentColor.current,
    labelColor: Color = MaterialTheme.colorScheme.tertiary,
    ltrText: Boolean = false
) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = alignment
    ) {
        BaseAppLabeledBodyText(
            label = label,
            text = text,
            color = color,
            labelColor = labelColor,
            ltrText = ltrText
        )
    }
}

@Composable
fun AppLabeledBodyTextVertical(
    modifier: Modifier = Modifier,
    label: String,
    text: String,
    alignment: Alignment.Horizontal = Alignment.Start,
    color: Color = LocalContentColor.current,
    labelColor: Color = MaterialTheme.colorScheme.tertiary,
    ltrText: Boolean = false,
    spacing: Float = Dimensions.Paddings.small
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(spacing.dp),
        alignment = alignment
    ) {
        BaseAppLabeledBodyText(
            label = label,
            text = text,
            color = color,
            labelColor = labelColor,
            ltrText = ltrText
        )
    }
}


@Composable
fun AppLabeledLabelTextHorizontal(
    modifier: Modifier = Modifier,
    label: String,
    text: String,
    alignment: Alignment.Vertical = Alignment.Top,
    color: Color = LocalContentColor.current,
    labelColor: Color = MaterialTheme.colorScheme.tertiary,
    ltrText: Boolean = false
) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.Start,
        alignment = alignment
    ) {
        BaseAppLabeledLabelText(
            label = "$label ",
            text = text,
            color = color,
            labelColor = labelColor,
            ltrText = ltrText
        )
    }
}

@Composable
fun AppLabeledTitleTextHorizontal(
    modifier: Modifier = Modifier,
    label: String,
    text: String,
    alignment: Alignment.Vertical = Alignment.Top,
    color: Color = LocalContentColor.current,
    labelColor: Color = LocalContentColor.current
) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = alignment
    ) {
        BaseAppLabeledTitleText(
            label = label,
            text = text,
            color = color,
            labelColor = labelColor
        )
    }
}

@Composable
fun AppLabeledSectionTitleTextHorizontal(
    modifier: Modifier = Modifier,
    label: String,
    text: String,
    alignment: Alignment.Vertical = Alignment.Top,
    color: Color = LocalContentColor.current,
    labelColor: Color = LocalContentColor.current
) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = alignment
    ) {
        BaseAppLabeledSectionTitleText(
            label = label,
            text = text,
            color = color,
            labelColor = labelColor
        )
    }
}

@Composable
private fun BaseAppLabeledBodyText(
    label: String,
    text: String,
    color: Color,
    labelColor: Color,
    ltrText: Boolean
) {
    AppBodyText(
        text = label,
        color = labelColor,
        fontWeight = FontWeight.Black
    )

    if (ltrText) {
        ForceLtr { AppBodyText(text = text, color = color) }
    } else {
        AppBodyText(text = text, color = color)
    }
}

@Composable
private fun BaseAppLabeledTitleText(
    label: String,
    text: String,
    color: Color,
    labelColor: Color
) {
    AppTitleText(
        text = label,
        color = labelColor,
    )

    AppTitleText(
        text = text,
        color = color,
    )
}

@Composable
private fun BaseAppLabeledSectionTitleText(
    label: String,
    text: String,
    color: Color,
    labelColor: Color
) {
    AppSectionTitleText(
        text = label,
        color = labelColor,
    )

    AppSectionTitleText(
        text = text,
        color = color,
    )
}

@Composable
private fun BaseAppLabeledLabelText(
    label: String,
    text: String,
    color: Color,
    labelColor: Color,
    ltrText: Boolean
) {
    AppLabelText(
        text = label,
        color = labelColor,
        fontWeight = FontWeight.Black
    )

    if (ltrText) {
        ForceLtr { AppLabelText(text = text, color = color) }
    } else {
        AppLabelText(text = text, color = color)
    }
}
