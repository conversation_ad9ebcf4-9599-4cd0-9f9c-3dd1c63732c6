package com.ryinex.accountant.client.shared.presentation.common.texts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.isShiftPressed
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.app
import com.ryinex.accountant.client.shared.data.common.utilities.appDouble
import com.ryinex.accountant.client.shared.data.common.utilities.fixDecimalText
import com.ryinex.accountant.client.shared.data.common.utilities.numberFormat
import com.ryinex.accountant.client.shared.presentation.common.state.StringState

@Composable
fun AppRichTextField(
    modifier: Modifier = Modifier,
    text: String,
    hint: String,
    enabled: Boolean,
    onValueChange: (String) -> Unit,
    singleLine: Boolean = false
) {
    OutlinedTextField(
        modifier = modifier.heightIn(min = Dimensions.RichTextField.minHeight.dp),
        value = text,
        label = { Text(hint) },
        enabled = enabled,
        onValueChange = onValueChange,
        singleLine = singleLine
    )
}

@Composable
fun AppTextField(
    modifier: Modifier = Modifier,
    state: StringState,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    maxLength: Int = Int.MAX_VALUE,
    helperText: String = "",
    errorText: String = "",
    minWidth: Float = remember { Dimensions.TextField.minWidth },
    onValueValidation: (String) -> Boolean = { true },
    singleLine: Boolean = false,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default
) {
    AppTextField(
        modifier = modifier,
        text = state.listen,
        hint = hint,
        enabled = enabled,
        isError = isError,
        helperText = helperText,
        errorText = errorText,
        visualTransformation = visualTransformation,
        maxLength = maxLength,
        minWidth = minWidth,
        onValueChange = state::update,
        onValueValidation = onValueValidation,
        singleLine = singleLine,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions
    )
}

@Composable
fun AppTextField(
    modifier: Modifier = Modifier,
    text: String,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    maxLength: Int = Int.MAX_VALUE,
    helperText: String = "",
    errorText: String = "",
    minWidth: Float = remember { Dimensions.TextField.minWidth },
    onValueChange: ((String) -> Unit)? = null,
    onValueValidation: (String) -> Boolean = { true },
    singleLine: Boolean = false,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default
) {
    BaseAppTextField(
        modifier = modifier,
        text = text,
        hint = hint,
        enabled = enabled,
        isError = isError,
        helperText = helperText,
        errorText = errorText,
        minWidth = minWidth,
        visualTransformation = visualTransformation,
        maxLength = maxLength,
        onValueChange = onValueChange,
        onValueValidation = onValueValidation,
        singleLine = singleLine,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        bottomContent = {}
    )
}

@Composable
fun AppDecimalTextField(
    modifier: Modifier = Modifier,
    amount: AppDouble,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    onlyPositive: Boolean = true,
    maxLength: Int = 15,
    helperText: String = "",
    errorText: String = "",
    minWidth: Float = remember { if (hint.length <= 12) Dimensions.TextField.minWidth / 2 else Dimensions.TextField.minWidth },
    onValueChange: ((AppDouble) -> Unit)? = null,
    singleLine: Boolean = false,
) {
    val initial = remember { if (amount == 0.0.app()) "" else amount.toString() }
    var fieldText by remember { mutableStateOf(initial) }

    LaunchedEffect(amount) {
        val amountStr = if (amount == 0.0.app()) "" else amount.toString()
        if (!fieldText.startsWith(amountStr)) {
            fieldText = amountStr
        }
    }

    AppDecimalTextField(
        modifier = modifier,
        text = fieldText,
        hint = hint,
        enabled = enabled,
        isError = isError,
        onlyPositive = onlyPositive,
        maxLength = maxLength,
        helperText = helperText,
        errorText = errorText,
        minWidth = minWidth,
        onValueChange = {
            fieldText = it
            val number = it.fixDecimalText().appDouble()
            if (number != null) onValueChange?.invoke(number)
        },
        singleLine = singleLine
    )
}

@Composable
private fun AppDecimalTextField(
    modifier: Modifier = Modifier,
    text: String,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    onlyPositive: Boolean = true,
    maxLength: Int = Int.MAX_VALUE,
    helperText: String = "",
    errorText: String = "",
    minWidth: Float = remember { if (hint.length <= 12) Dimensions.TextField.minWidth / 2 else Dimensions.TextField.minWidth },
    onValueChange: ((String) -> Unit)? = null,
    singleLine: Boolean = false,
) {
    val regex = remember {
        if (onlyPositive) {
            "[0-9]*\\.?[0-9]{0,2}".toRegex()
        } else {
            "-?[0-9]*\\.?[0-9]{0,2}".toRegex()
        }
    }
    BaseAppTextField(
        modifier = modifier,
        text = text,
        hint = hint,
        enabled = enabled,
        isError = isError,
        helperText = "${text.numberFormat()}\n$helperText",
        errorText = errorText,
        minWidth = minWidth,
        maxLength = maxLength,
        onValueValidation = { it.isBlank() || it.matches(regex) },
        onValueChange = {
            onValueChange?.invoke(it)
        },
        singleLine = singleLine,
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
        bottomContent = {},
        forceLtr = true
    )
}

@Composable
fun AppIntegerTextField(
    modifier: Modifier = Modifier,
    text: String,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    onlyPositive: Boolean = true,
    maxLength: Int = Int.MAX_VALUE,
    helperText: String = "",
    errorText: String = "",
    minWidth: Float = remember { if (hint.length <= 15) Dimensions.TextField.minWidth / 2 else Dimensions.TextField.minWidth },
    onValueChange: ((String) -> Unit)? = null,
    singleLine: Boolean = false,
) {
    val regex = remember {
        if (onlyPositive) {
            "[0-9]*".toRegex()
        } else {
            "-?[0-9]*".toRegex()
        }
    }

    BaseAppTextField(
        modifier = modifier,
        text = text,
        hint = hint,
        enabled = enabled,
        isError = isError,
        helperText = helperText,
        errorText = errorText,
        minWidth = minWidth,
        maxLength = maxLength,
        onValueValidation = { it.isBlank() || it.matches(regex) },
        onValueChange = {
            onValueChange?.invoke(it)
        },
        singleLine = singleLine,
        bottomContent = {},
        forceLtr = true
    )
}

@Composable
internal fun BaseAppTextField(
    modifier: Modifier = Modifier,
    text: String,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    maxLength: Int = Int.MAX_VALUE,
    helperText: String = "",
    errorText: String = "",
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    focusRequester: FocusRequester = remember { FocusRequester() },
    minWidth: Float = remember { Dimensions.TextField.minWidth },
    maxWidth: Float = minWidth,
    onWidthChanged: (Float) -> Unit = {},
    onValueValidation: (String) -> Boolean = { true },
    onValueChange: ((String) -> Unit)? = null,
    singleLine: Boolean = false,
    forceLtr: Boolean = false,
    bottomContent: @Composable () -> Unit
) {
    var isFocused by remember { mutableStateOf(false) }
    AppColumn(
        arrangement = Arrangement.Top,
        alignment = Alignment.Start
    ) {
        OutlinedTextField(
            modifier = modifier
                .focusRequester(focusRequester)
                .onFocusChanged { isFocused = it.isFocused }
                .onGloballyPositioned { onWidthChanged(it.size.width.toFloat()) }
                .then(if (singleLine) Modifier else Modifier.moveFocusOnTab()),
            value = text,
            enabled = enabled,
            textStyle = MaterialTheme.typography.bodyMedium,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            visualTransformation = visualTransformation,
            singleLine = singleLine,
            label = { AppBodyText(text = hint) },
            onValueChange = {
                if (onValueChange != null && it.length <= maxLength && onValueValidation(it)) {
                    onValueChange.invoke(it)
                }
            },
            isError = isError && isFocused,
            supportingText = {
                val currentColor = LocalContentColor.current
                val errorColor = MaterialTheme.colorScheme.error

                val text = remember(isError, errorText, helperText) {
                    buildAnnotatedString {
                        if (isError) withStyle(SpanStyle(color = errorColor)) { append("${errorText.trim()}\n") }
                        withStyle(SpanStyle(color = currentColor)) { append(helperText.trim()) }
                    }
                }

                if (text.isNotBlank()) AppLabelText(text = text)
            }
        )

        bottomContent()
    }
}


@OptIn(ExperimentalComposeUiApi::class)
fun Modifier.moveFocusOnTab() = composed {
    val focusManager = LocalFocusManager.current
    onPreviewKeyEvent {
        if (it.type == KeyEventType.KeyDown && it.key == Key.Tab) {
            focusManager.moveFocus(
                if (it.isShiftPressed) FocusDirection.Previous else FocusDirection.Next
            )
            true
        } else {
            false
        }
    }
}

fun Modifier.onEnterPress(enabled: Boolean, onPress: () -> Unit) = composed {
    return@composed if (enabled) {
        this.onPreviewKeyEvent {
            val isEnter = it.key == Key.Enter || it.key == Key.NumPadEnter
            if (isEnter) {
                onPress()
                return@onPreviewKeyEvent true
            }
            false
        }
    } else {
        this
    }
}