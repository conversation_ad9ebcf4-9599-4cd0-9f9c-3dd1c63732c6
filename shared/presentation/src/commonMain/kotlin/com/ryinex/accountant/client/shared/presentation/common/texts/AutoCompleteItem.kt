package com.ryinex.accountant.client.shared.presentation.common.texts

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.isShiftPressed
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.PopupProperties
import com.ryinex.accountant.client.shared.presentation.common.checkbox.AppCheckbox
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.selection.TitledSelectedItem
import com.ryinex.accountant.client.shared.data.common.utilities.searchNormalized

private data class AutoCompleteItem<T>(
    val item: T,
    val display: String,
    val search: String
)

@Composable
fun <T> AppSelectableAutoCompleteTextField(
    modifier: Modifier = Modifier,
    text: String,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    minSuggestLength: Int = 0,
    errorText: String = "",
    minWidth: Float = remember { Dimensions.TextField.minWidth },
    onValueChange: (String) -> Unit,
    singleLine: Boolean = false,
    isCheckable: Boolean = false,
    items: List<T>,
    selected: T?,
    onCancelSelected: () -> Unit,
    isCancelSelectedEnabled: Boolean = true,
    allItems: List<T> = items,
    isCheckableMapper: (T) -> Boolean = { false },
    checkedMapper: (T) -> Boolean = { false },
    searchMapper: (T) -> String,
    displayMapper: (T) -> String,
    onItemClick: (T) -> Unit,
    onEmptyItemClick: ((String) -> Unit)?
) {
    AppColumn(
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.Start
    ) {
        if (selected == null) {
            AppAutoCompleteTextField(
                modifier = modifier,
                text = text,
                hint = hint,
                enabled = enabled,
                isError = isError,
                minSuggestLength = minSuggestLength,
                errorText = errorText,
                minWidth = minWidth,
                onValueChange = onValueChange,
                singleLine = singleLine,
                isCheckable = isCheckable,
                items = items,
                allItems = allItems,
                searchMapper = searchMapper,
                displayMapper = displayMapper,
                onItemClick = onItemClick,
                isCheckableMapper = isCheckableMapper,
                checkedMapper = checkedMapper,
                onEmptyItemClick = onEmptyItemClick
            )
        }

        if (selected != null) {
            TitledSelectedItem(
                title = hint,
                onCancel = onCancelSelected,
                cancelEnabled = isCancelSelectedEnabled,
                text = displayMapper(selected)
            )
        }
    }
}

//@Composable
//fun <T> AppAutoCompleteTextField(
//    modifier: Modifier = Modifier,
//    text: String,
//    hint: String,
//    enabled: Boolean,
//    isError: Boolean,
//    minSuggestLength: Int = 0,
//    errorText: String = "",
//    minWidth: Float = remember { Dimensions.TextField.minWidth },
//    onValueChange: (String) -> Unit,
//    singleLine: Boolean = false,
//    isCheckable: Boolean = false,
//    items: List<T>,
//    allItems: List<T> = items,
//    isCheckableMapper: (T) -> Boolean = { false },
//    checkedMapper: (T) -> Boolean = { false },
//    searchMapper: (T) -> String,
//    displayMapper: (T) -> String,
//    onItemClick: (T) -> Unit,
//    onEmptyItemClick: ((String) -> Unit)?
//) {
//    var isFocused by remember { mutableStateOf(false) }
//    val queryText = remember(text){ text.trim() }
//    val allMapped = remember(allItems) {
//        allItems.map { AutoCompleteItem(item = it, display = displayMapper(it), search = searchMapper(it)) }
//    }
//    val mapped = remember(items) {
//        items.map { AutoCompleteItem(item = it, display = displayMapper(it), search = searchMapper(it)) }
//    }
//    val mappedFiltered = remember(queryText) {
//        if (queryText.length < minSuggestLength) return@remember emptyList()
//        mapped.filter {
//            val search = it.search.searchNormalized().split(" ").distinct()
//            val text = queryText.searchNormalized().split(" ").distinct()
//            val matches = search.filter { itemText -> text.any { searchText -> itemText.contains(searchText) } }
//
//            matches.size >= text.size
//        }
//    }
//    val isExpanded by remember(isFocused, mappedFiltered) {
//        mutableStateOf(isFocused)
//    }
//    val focusManager = LocalFocusManager.current
//    var maxDropDownWidth by remember { mutableStateOf(Dimensions.TextField.minWidth) }
//    val shouldAddNew = remember(text) {
//        onEmptyItemClick != null && queryText.isNotBlank() && (mappedFiltered.isEmpty() && allMapped.none { it.search.searchNormalized() == queryText.searchNormalized() })
//    }
//
//    TextField(
//        modifier = modifier
//            .onFocusChanged {
//                isFocused = it.isFocused
//            }.onPreviewKeyEvent {
//                val isEnter = it.key == Key.Enter || it.key == Key.NumPadEnter
//                if (isEnter && mappedFiltered.isNotEmpty()) {
//                    if (!isCheckable) focusManager.clearFocus()
//                    onItemClick(mappedFiltered.first().item)
//                    return@onPreviewKeyEvent true
//                } else if (isEnter && shouldAddNew) {
//                    focusManager.clearFocus()
//                    onEmptyItemClick?.invoke(queryText)
//                    return@onPreviewKeyEvent true
//                }
//                false
//            }
//            .onGloballyPositioned {
//                maxDropDownWidth = it.size.width.toFloat()
//            }
//            .moveFocusOnTab(),
//        value = queryText,
//        onValueChange = onValueChange,
//        label = { Text(hint) },
//        placeholder = { Text(hint) },
//        isError = isError,
//        keyboardOptions = KeyboardOptions(
//            keyboardType = KeyboardType.Text
//        ),
//        enabled = enabled,
//        singleLine = singleLine,
//        supportingText = {
//            if (queryText.length >= minSuggestLength) {
//                ItemsDropDown(
//                    modifier = Modifier
//                        .widthIn(min = maxDropDownWidth.dp, max = maxDropDownWidth.dp)
//                        .heightIn(max = 200.dp),
//                    isCheckable = isCheckable,
//                    currentText = queryText,
//                    expanded = isExpanded,
//                    allItems = allMapped,
//                    filteredItems = mappedFiltered,
//                    checkedMapper = checkedMapper,
//                    onDismiss = {
//                        focusManager.clearFocus()
//                    },
//                    onItemClick = {
//                        if (!isCheckable) focusManager.clearFocus()
//                        onItemClick(it)
//                    },
//                    addNew = shouldAddNew,
//                    isCheckableMapper = isCheckableMapper,
//                    onEmptyItemClick = {
//                        if (isError) return@ItemsDropDown
//                        focusManager.clearFocus()
//                        onEmptyItemClick?.invoke(it)
//                    }
//                )
//            }
//        }
//    )
//}

@Composable
fun <T> AppAutoCompleteTextField(
    modifier: Modifier = Modifier,
    text: String,
    hint: String,
    enabled: Boolean,
    isError: Boolean,
    minSuggestLength: Int = 0,
    errorText: String = "",
    minWidth: Float = remember { Dimensions.TextField.minWidth },
    onValueChange: (String) -> Unit,
    singleLine: Boolean = false,
    isCheckable: Boolean = false,
    items: List<T>,
    allItems: List<T> = items,
    isCheckableMapper: (T) -> Boolean = { false },
    checkedMapper: (T) -> Boolean = { false },
    searchMapper: (T) -> String,
    displayMapper: (T) -> String,
    onItemClick: (T) -> Unit,
    onEmptyItemClick: ((String) -> Unit)?
) {
    var isFocused by remember { mutableStateOf(false) }
    val queryText = remember(text){ text.trim() }
    val allMapped = remember(allItems) {
        allItems.map { AutoCompleteItem(item = it, display = displayMapper(it), search = searchMapper(it)) }
    }
    val mapped = remember(items) {
        items.map { AutoCompleteItem(item = it, display = displayMapper(it), search = searchMapper(it)) }
    }
    val mappedFiltered = remember(queryText) {
        if (queryText.length < minSuggestLength) return@remember emptyList()
        mapped.filter {
            val search = it.search.searchNormalized().split(" ").distinct()
            val text = queryText.searchNormalized().split(" ").distinct()
            val matches = search.filter { itemText -> text.any { searchText -> itemText.contains(searchText) } }

            matches.size >= text.size
        }
    }
    val isExpanded by remember(isFocused, mappedFiltered) {
        mutableStateOf(isFocused)
    }
    val focusManager = LocalFocusManager.current
    var maxDropDownWidth by remember { mutableStateOf(Dimensions.TextField.minWidth) }
    val shouldAddNew = remember(text) {
        onEmptyItemClick != null && queryText.isNotBlank() && (mappedFiltered.isEmpty() && allMapped.none { it.search.searchNormalized() == queryText.searchNormalized() })
    }
    BaseAppTextField(
        modifier = modifier
            .onFocusChanged {
                isFocused = it.isFocused
            }.onPreviewKeyEvent {
                val isEnter = it.key == Key.Enter || it.key == Key.NumPadEnter
                if (isEnter && mappedFiltered.isNotEmpty()) {
                    if (!isCheckable) focusManager.clearFocus()
                    onItemClick(mappedFiltered.first().item)
                    return@onPreviewKeyEvent true
                } else if (isEnter && shouldAddNew) {
                    focusManager.clearFocus()
                    onEmptyItemClick?.invoke(queryText)
                    return@onPreviewKeyEvent true
                }
                false
            }.moveFocusOnTab(),
        text = queryText,
        hint = hint,
        enabled = enabled,
        isError = isError,
        errorText = errorText,
        minWidth = minWidth,
        onValueChange = onValueChange,
        singleLine = singleLine,
        onWidthChanged = {
            maxDropDownWidth = it
        },
        bottomContent = {
            if (queryText.length >= minSuggestLength) {
                ItemsDropDown(
                    modifier = Modifier
                        .widthIn(min = maxDropDownWidth.dp, max = maxDropDownWidth.dp)
                        .heightIn(max = 80.dp),
                    isCheckable = isCheckable,
                    currentText = queryText,
                    expanded = isExpanded,
                    allItems = allMapped,
                    filteredItems = mappedFiltered,
                    checkedMapper = checkedMapper,
                    onDismiss = {
                        // focusManager.clearFocus()
                    },
                    onItemClick = {
                        if (!isCheckable) focusManager.clearFocus()
                        onItemClick(it)
                    },
                    addNew = shouldAddNew,
                    isCheckableMapper = isCheckableMapper,
                    onEmptyItemClick = {
                        focusManager.clearFocus()
                        onEmptyItemClick?.invoke(it)
                    }
                )
            }
        }
    )
}

@Composable
private fun <T> ItemsDropDown(
    modifier: Modifier = Modifier,
    currentText: String,
    expanded: Boolean,
    isCheckable: Boolean,
    allItems: List<AutoCompleteItem<T>>,
    filteredItems: List<AutoCompleteItem<T>>,
    addNew: Boolean,
    isCheckableMapper: (T) -> Boolean,
    checkedMapper: (T) -> Boolean,
    onDismiss: () -> Unit,
    onItemClick: (T) -> Unit,
    onEmptyItemClick: (String) -> Unit,
) {
    val scrollState = rememberScrollState()
    DropdownMenu(
        modifier = modifier,
        expanded = expanded,
        scrollState = scrollState,
        onDismissRequest = onDismiss,
        properties = PopupProperties(
            dismissOnClickOutside = true
        )
    ) {
        filteredItems.forEachIndexed { index, item ->
            AppRow(
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {
                if (isCheckable) {
                    val checkable = remember(item.item) { isCheckableMapper(item.item) }
                    AppCheckbox(
                        enabled = checkable,
                        checked = checkedMapper(item.item),
                        onCheckedChange = { onItemClick(item.item) }
                    )

                    AppBodyText(
                        text = item.display,
                        modifier = Modifier
                            .padding(horizontal = Dimensions.Paddings.small.dp)
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(Dimensions.Paddings.small))
                            .clickable(checkable) { onItemClick(item.item) }
                    )
                } else {
                    AppBodyText(
                        text = item.display,
                        modifier = Modifier
                            .padding(horizontal = Dimensions.Paddings.small.dp)
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(Dimensions.Paddings.small))
                            .clickable { onItemClick(item.item) }
                    )
                }
            }
        }

        if (addNew) {
            DropdownMenuItem(
                modifier = Modifier,
                text = { AppBodyText(text = "إضافة جديد \"$currentText\"") },
                onClick = {
                    onEmptyItemClick(currentText)
                }
            )
        }
    }
}

@Composable
fun Modifier.moveFocusOnTab(
    focusManager: FocusManager = LocalFocusManager.current
) = onPreviewKeyEvent {
    if (it.type == KeyEventType.KeyDown && it.key == Key.Tab) {
        focusManager.moveFocus(
            if (it.isShiftPressed) FocusDirection.Previous
            else FocusDirection.Next
        )
        return@onPreviewKeyEvent true
    }
    false
}