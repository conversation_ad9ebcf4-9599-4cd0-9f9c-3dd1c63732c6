package com.ryinex.accountant.client.shared.presentation.customers.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_edit
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.data.customers.models.Customer
import com.ryinex.accountant.client.shared.domain.customers.details.models.ScreenState
import com.ryinex.accountant.client.shared.domain.customers.details.ViewModel
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.projects.models.ProjectIncome
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.tables.customersTransactionsTable
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource

class CustomerTransactionsScreen(private val customer: Customer) : BrowserScreen {

    override val urlSegment: String = "${customer.id}"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                projects = TempDI.projects,
                customers = TempDI.customers,
                users = TempDI.users,
                coroutinesContext = emptyCoroutineContext
            )
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initTransactionsState).value
        LaunchedEffect(Unit) { viewModel.initScreen(customer) }

        AppBasePage(
            title = "العملاء",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = state.isBackEnabled,
            isPullToRefreshEnabled = true,
            isPullToRefresh = !state.isBackEnabled,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = { navigator?.browserPop() }
        ) {
            val scope = rememberCoroutineScope()
            CustomerTransactionsScreenContent(
                state = state,
                viewModel = viewModel
            )

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }
}

@Composable
private fun CustomerTransactionsScreenContent(
    modifier: Modifier = Modifier,
    state: ScreenState,
    viewModel: ViewModel
) {
    val isDesktop = LocalIsDesktop.current
    val payIncome = remember { mutableStateOf<ProjectIncome?>(null) }
    val editIncome = remember { mutableStateOf<ProjectIncome?>(null) }
    val lazyState = rememberLazyListState()

    val table = customersTransactionsTable(
        lazyState = lazyState,
        items = state.transactions,
        isEditEnabled = state.isBackEnabled,
        isPaymentEnabled = state.isEditEnabled,
        onEdit = { item -> editIncome.value = item },
        onPay = { item -> payIncome.value = item }
    )
    AppLazyColumn(
        modifier = modifier.fillMaxSize(),
        lazyState = lazyState,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
        alignment = Alignment.Start
    ) { horizontalScrollState ->
        item {
            var showEditSheet by remember { mutableStateOf(false) }

            AppSurface(
                modifier = Modifier.sectionsSpacing().shadowBluish(),
                padding = PaddingValues(Dimensions.Paddings.medium.dp),
            ) {
                AppColumn(
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                    alignment = Alignment.Start
                ) {
                    AppRow(
                        modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
                        arrangement = Arrangement.SpaceBetween,
                        alignment = Alignment.CenterVertically
                    ) {

                        Box(
                            modifier = Modifier.fillMaxHeight().weight(1f),
                            contentAlignment = Alignment.CenterStart
                        ) {
                            AppSectionTitleText(
                                state.customer?.name ?: "",
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

                        if (state.isCustomerDetailsEnabled) {
                            AppTextButton(
                                modifier = Modifier,
                                text = "تعديل",
                                enabled = state.isCustomerDetailsEnabled,
                                iconPainter = painterResource(Res.drawable.ic_edit),
                                onClick = { showEditSheet = true }
                            )
                        }
                    }

                    AppLabeledBodyTextHorizontal(label = "الهاتف", text = state.customer?.phoneNumber ?: "")
                }
            }

            if (showEditSheet) {
                CustomerSheet(
                    customer = state.customer,
                    onDismissRequest = { showEditSheet = false },
                    onFinish = { viewModel.refreshScreen() }
                )
            }
        }

        item {
            AppSectionTitleText(text = "حركة التحويلات", modifier = Modifier.sectionsSpacing())
        }

        CustomersTransactionsListSection(
            table = table,
            scrollState = horizontalScrollState,
            transactions = state.transactions,
            isPaymentEnabled = state.isBackEnabled,
            isEditEnabled = state.isEditEnabled,
            isDesktop = isDesktop,
            onFinishPayment = { viewModel.refreshScreen() },
            onRefresh = { viewModel.refreshScreen() }
        )
    }


    CustomersTransactionsListSectionBottomSheets(
        payIncome = payIncome,
        editIncome = editIncome,
        onFinishPayment = { viewModel.refreshScreen() }
    )
}
