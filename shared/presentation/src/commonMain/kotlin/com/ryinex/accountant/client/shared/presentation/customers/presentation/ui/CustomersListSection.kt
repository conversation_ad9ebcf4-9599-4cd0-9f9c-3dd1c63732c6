package com.ryinex.accountant.client.shared.presentation.customers.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.home.presentation.ui.NextArrowButton
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.data.customers.models.Customer
import com.ryinex.accountant.client.shared.domain.customers.home.ViewModel
import com.ryinex.accountant.client.shared.domain.customers.home.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish

internal fun LazyStaggeredGridScope.CustomersListSection(
    customers: List<Customer>,
    viewModel: ViewModel,
    state: ScreenState
) {
    state.customers.forEach { customer ->
        item {
            val navigator = LocalNavigator.current

            CustomerCard(
                modifier = Modifier.then(
                    if (LocalIsDesktop.current) Modifier.width(Dimensions.Cards.maxCardWidthDesktop)
                    else Modifier.fillMaxWidth()
                ),
                name = customer.name,
                phoneNumber = customer.phoneNumber,
                enabled = state.isCustomerDetailsEnabled,
                onClick = { navigator?.browserPush(CustomerTransactionsScreen(customer)) }
            )
        }
    }
}

@Composable
fun CustomerCard(
    modifier: Modifier = Modifier,
    name: String,
    phoneNumber: String,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppCard(
        modifier = modifier.fillMaxWidth().shadowBluish(),
        enabled = enabled,
        onClick = onClick,
        padding = Dimensions.Paddings.medium.dp,
        regularColors = true
    ) {
        AppRow(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.CenterVertically
        ) {
            AppColumn(
                modifier = Modifier.weight(1f),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
                alignment = Alignment.Start
            ) {
                AppSubSectionTitleText(text = name, color = MaterialTheme.colorScheme.primary)

                AppLabeledBodyTextHorizontal(label = "الهاتف", text = phoneNumber)
            }

            NextArrowButton(
                enabled = enabled,
                onClick = onClick
            )
        }
    }
}