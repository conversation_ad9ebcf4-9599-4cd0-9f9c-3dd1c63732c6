package com.ryinex.accountant.client.shared.presentation.home.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_logout_2
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.resources.ic_add
import com.ryinex.accountant.client.resources.illustration_bussines_plan
import com.ryinex.accountant.client.resources.illustration_client_deal
import com.ryinex.accountant.client.resources.illustration_organization_2
import com.ryinex.accountant.client.resources.illustration_terms
import com.ryinex.accountant.client.resources.illustration_users
import com.ryinex.accountant.client.resources.illustration_wallet
import com.ryinex.accountant.client.resources.menu_beneficiaries_description
import com.ryinex.accountant.client.resources.menu_clients_description
import com.ryinex.accountant.client.resources.menu_organization_description
import com.ryinex.accountant.client.resources.menu_projects_description
import com.ryinex.accountant.client.resources.menu_terms_description
import com.ryinex.accountant.client.resources.menu_users_description
import com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui.BeneficiariesScreen
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyStaggeredGrid
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.feature.components.adaptWidth
import com.ryinex.accountant.client.shared.presentation.common.images.AppOutlinedIconButton
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledSectionTitleTextHorizontal
import com.ryinex.accountant.client.shared.presentation.customers.presentation.ui.CustomersScreen
import com.ryinex.accountant.client.shared.presentation.organization.presentation.ui.OrganizationScreen
import com.ryinex.accountant.client.shared.presentation.projects.presentation.ui.ProjectsScreen
import com.ryinex.accountant.client.shared.presentation.terms.CombinedTermsScreen
import com.ryinex.accountant.client.shared.presentation.users.UsersScreen
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.domain.home.ViewModel
import com.ryinex.accountant.client.shared.domain.home.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppFAB
import com.ryinex.accountant.client.shared.presentation.projects.presentation.ui.ProjectExpenseSheet
import core.common.status.Status
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

@OptIn(ExperimentalMaterial3Api::class)
class HomeScreen : BrowserScreen {

    override val urlSegment: String = "home"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                TempDI.status,
                TempDI.projects,
                TempDI.users,
                TempDI.analytics,
                TempDI.auth,
                emptyCoroutineContext
            )
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initState).value
        LaunchedEffect(Unit) { viewModel.initScreen() }

        AppBasePage(
            title = "",
            screenPadding = PaddingValues(),
            fab = { FAB(state) },
            isBackEnabled = false,
            isPullToRefreshEnabled = true,
            isPullToRefresh = state.isPullToRefresh,
            onPullToRefresh = { viewModel.refreshScreen() },
            actions = {
                AppOutlinedIconButton(
                    painter = painterResource(Res.drawable.ic_logout_2),
                    enabled = true,
                    onClick = {
                        viewModel.logout()
                        navigator?.browserPop()
                    }
                )
            }
        ) {
            val scope = rememberCoroutineScope()
            HomeScreenContent(
                state = state,
            )

            AppStatusBar(
                modifier = Modifier.padding(horizontal = Dimensions.Paddings.screen.dp),
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }

    @Composable
    private fun FAB(state: ScreenState) {
        var showAddSheet by remember { mutableStateOf(false) }

        AppFAB(
            onClick = { showAddSheet = true },
            enabled = state.status.none { it is Status.Loading },
            icon = painterResource(Res.drawable.ic_add)
        )

        if (showAddSheet) {
            ProjectExpenseSheet(
                project = null,
                onDismissRequest = { showAddSheet = false },
                expense = null,
                onFinish = { showAddSheet = false }
            )
        }
    }
}

@Composable
private fun HomeScreenContent(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    val navigator = LocalNavigator.current
    val gridSpacing = remember { Dimensions.Paddings.large.dp }

    AppLazyStaggeredGrid(
        modifier = modifier.fillMaxSize(),
        spacing = gridSpacing,
        alignment = Arrangement.spacedBy(Dimensions.Paddings.large.dp),
    ) {

        item(
            span = StaggeredGridItemSpan.FullLine
        ) {
            AppLabeledSectionTitleTextHorizontal(
                modifier = Modifier.sectionsSpacing().padding(horizontal = Dimensions.Paddings.screen.dp / 2),
                label = "مرحباً",
                text = state.loggedInUser?.name ?: "",
                color = MaterialTheme.colorScheme.tertiary,
            )
        }

        if (state.loggedInUser?.isAdmin == true) {
            item {
                BannerCarouselWidget(
                    state = state,
                    gridSpacing = gridSpacing
                )
            }

            item {
                HomeScreenGridChoiceItem(
                    modifier = Modifier.adaptWidth().padding(horizontal = Dimensions.Paddings.screen.dp / 2),
                    title = "المؤسسة",
                    description = stringResource(Res.string.menu_organization_description),
                    icon = Res.drawable.illustration_organization_2,
                    onClick = {
                        navigator?.browserPush(OrganizationScreen())
                    }
                )
            }
        }

        item {
            HomeScreenGridChoiceItem(
                modifier = Modifier.adaptWidth().padding(horizontal = Dimensions.Paddings.screen.dp / 2),
                title = "الأعمال",
                description = stringResource(Res.string.menu_projects_description),
                icon = Res.drawable.illustration_bussines_plan,
                onClick = {
                    navigator?.browserPush(ProjectsScreen())
                }
            )
        }

        if (state.loggedInUser?.isAdmin == true) {
            item {
                HomeScreenGridChoiceItem(
                    modifier = Modifier.adaptWidth().padding(horizontal = Dimensions.Paddings.screen.dp / 2),
                    title = "المستخدمين",
                    description = stringResource(Res.string.menu_users_description),
                    icon = Res.drawable.illustration_users,
                    onClick = {
                        navigator?.browserPush(UsersScreen())
                    }
                )
            }

            item {
                HomeScreenGridChoiceItem(
                    modifier = Modifier.adaptWidth().padding(horizontal = Dimensions.Paddings.screen.dp / 2),
                    title = "العملاء",
                    description = stringResource(Res.string.menu_clients_description),
                    icon = Res.drawable.illustration_client_deal,
                    onClick = {
                        navigator?.browserPush(CustomersScreen())
                    }
                )
            }

            item {
                HomeScreenGridChoiceItem(
                    modifier = Modifier.adaptWidth().padding(horizontal = Dimensions.Paddings.screen.dp / 2),
                    title = "المستفيدين",
                    description = stringResource(Res.string.menu_beneficiaries_description),
                    icon = Res.drawable.illustration_wallet,
                    onClick = {
                        navigator?.browserPush(BeneficiariesScreen())
                    }
                )
            }
        }

        if (state.loggedInUser?.isAdmin == true) {
            item {
                HomeScreenGridChoiceItem(
                    modifier = Modifier.adaptWidth().padding(horizontal = Dimensions.Paddings.screen.dp / 2),
                    title = "بنود الأعمال",
                    description = stringResource(Res.string.menu_terms_description),
                    icon = Res.drawable.illustration_terms,
                    onClick = {
                        navigator?.browserPush(CombinedTermsScreen())
                    }
                )
            }
        }
    }
}

