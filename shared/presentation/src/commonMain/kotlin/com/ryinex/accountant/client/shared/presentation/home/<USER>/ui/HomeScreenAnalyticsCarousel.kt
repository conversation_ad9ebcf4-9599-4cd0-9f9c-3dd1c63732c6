package com.ryinex.accountant.client.shared.presentation.home.presentation.ui

import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.illustration_coins
import com.ryinex.accountant.client.resources.illustration_incomes
import com.ryinex.accountant.client.shared.domain.home.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun BannerCarouselWidget(
    state: ScreenState,
    gridSpacing: Dp,
    modifier: Modifier = Modifier
) {
    val pagerState = rememberPagerState(pageCount = { 2 })

    LaunchedEffect(Unit) {
        while (isActive) {
            delay(7500)
            pagerState.animateScrollToPage(
                page = (pagerState.currentPage + 1) % 2,
                animationSpec = tween(durationMillis = 1000)
            )
        }
    }

    Box(
        contentAlignment = Alignment.BottomCenter,
        modifier = modifier.clip(CardDefaults.shape)
    ) {
        HorizontalPager(
            state = pagerState,
            contentPadding = getContentPadding(pagerState.currentPage, gridSpacing),
            pageSpacing = gridSpacing,
            verticalAlignment = Alignment.Top,
            beyondViewportPageCount = 1
        ) { page ->
            if (page == 0) {
                HomeScreenAnalyticsItem(
                    modifier = Modifier.fillMaxWidth(),
                    title = "مصاريف الاسبوع",
                    value = state.analytics.expensesThisWeek,
                    lastWeekValue = state.analytics.expensesLastWeek,
                    percentage = state.analytics.expensesPercentage,
                    gridSpacing = gridSpacing,
                    res = Res.drawable.illustration_coins
                )
            } else {
                HomeScreenAnalyticsItem(
                    modifier = Modifier.fillMaxWidth(),
                    title = "إيرادات الاسبوع",
                    value = state.analytics.incomesThisWeek,
                    lastWeekValue = state.analytics.incomesLastWeek,
                    percentage = state.analytics.incomesPercentage,
                    gridSpacing = gridSpacing,
                    res = Res.drawable.illustration_incomes
                )
            }
        }

        Row(
            Modifier
                .align(Alignment.BottomCenter)
                .background(color = Color.Black.copy(0.2f), shape = CircleShape)
                .padding(vertical = gridSpacing / 4)
                .padding(horizontal = gridSpacing * 1),
            horizontalArrangement = Arrangement.Center
        ) {
            repeat(pagerState.pageCount) { iteration ->
                val color =
                    if (pagerState.currentPage == iteration) MaterialTheme.colorScheme.tertiary else MaterialTheme.colorScheme.primary
                Box(
                    modifier = Modifier
                        .padding(2.dp)
                        .clip(CircleShape)
                        .background(color)
                        .size(8.dp)
                )
            }
        }
    }
}

private fun getContentPadding(currentPage: Int, spacing: Dp): PaddingValues {
    return PaddingValues(
        start = if (currentPage == 0) Dimensions.Paddings.screen.dp else spacing * 3,
        end = if (currentPage == 0) spacing * 3 else Dimensions.Paddings.screen.dp
    )
}