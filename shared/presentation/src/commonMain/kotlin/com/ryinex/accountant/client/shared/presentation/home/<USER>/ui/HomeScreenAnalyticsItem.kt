package com.ryinex.accountant.client.shared.presentation.home.presentation.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.formattedMinimum
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppImage
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppFlowRow
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportIconImageView
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowPinkish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import org.jetbrains.compose.resources.DrawableResource

@Composable
internal fun HomeScreenAnalyticsItem(
    modifier: Modifier = Modifier,
    title: String,
    value: AppDouble,
    lastWeekValue: AppDouble,
    percentage: AppDouble,
    gridSpacing: Dp,
    res: DrawableResource,
    minHeight: Dp = Dimensions.HomeScreen.gridItemMinHeight * 2 + gridSpacing,
) {
    AppCard(
        modifier = modifier
            .heightIn(min = if (LocalIsDesktop.current) minHeight else Dimensions.HomeScreen.gridItemMinHeight)
            .fillMaxSize()
            .shadowBluish(),
        enabled = false,
        onClick = {}
    ) {
        AppRow(
            modifier = Modifier
                .fillMaxSize()
                .fillMaxHeight()
                .padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.CenterHorizontally),
            alignment = Alignment.CenterVertically
        ) {
            TextColumn(Modifier.weight(1f), title, value, percentage, lastWeekValue)

            AppCard(
                modifier = Modifier.size(120.dp).shadowPinkish(60.dp),
                shape = CircleShape
            ) {
                Box(
                    modifier = Modifier.fillMaxSize().background(Color.White),
                    contentAlignment = Alignment.Center
                ) {
                    AppImage(res = res, contentScale = ContentScale.Inside)
                }

            }
        }
    }
}

@Composable
private fun TextColumn(
    modifier: Modifier = Modifier,
    title: String,
    value: AppDouble,
    percentage: AppDouble,
    lastWeekValue: AppDouble
) {
    AppColumn(
        modifier = modifier
            .fillMaxHeight()
            .padding(Dimensions.Paddings.medium.dp),
        arrangement = Arrangement.SpaceBetween,
        alignment = Alignment.Start
    ) {
        AppSubSectionTitleText(
            text = title,
            color = MaterialTheme.colorScheme.primary,
        )

        AppColumn(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                text = value.formatted(),
                color = MaterialTheme.colorScheme.tertiary,
                fontWeight = FontWeight.Bold
            )

            AppSurface(
                shape = CircleShape,
                padding = PaddingValues(horizontal = Dimensions.Paddings.medium.dp),
            ) {
                AppRow(
                    modifier = Modifier,
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                    alignment = Alignment.CenterVertically
                ) {
                    ForceLtr {
                        AppBodyText(
                            text = "${percentage.formattedMinimum()} %",
                            color = MaterialTheme.colorScheme.tertiary
                        )
                    }

                    if (percentage > 100) {
                        AppSupportIconImageView(
                            painter = rememberVectorPainter(Icons.Default.KeyboardArrowUp),
                            iconOnly = true,
                            backgroundColor = MaterialTheme.colorScheme.tertiary
                        )
                    } else if (percentage < 100) {
                        AppSupportIconImageView(
                            painter = rememberVectorPainter(Icons.Default.KeyboardArrowDown),
                            iconOnly = true,
                            backgroundColor = MaterialTheme.colorScheme.tertiary
                        )
                    }
                }
            }
        }

        AppFlowRow(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.SpaceBetween,
            alignment = Arrangement.spacedBy(Dimensions.Paddings.small.dp)
        ) {
            AppTitleText(
                text = "الإسبوع الماضي",
            )

            AppTitleText(
                text = lastWeekValue.formatted(),
                fontWeight = FontWeight.Bold
            )
        }
    }
}