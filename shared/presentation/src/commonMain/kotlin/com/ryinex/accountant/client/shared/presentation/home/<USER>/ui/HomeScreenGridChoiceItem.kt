package com.ryinex.accountant.client.shared.presentation.home.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_organization
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppImage
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppIconImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
internal fun HomeScreenGridChoiceItem(
    modifier: Modifier = Modifier,
    title: String,
    description: String,
    icon: DrawableResource,
    onClick: () -> Unit,
    enabled: Boolean = true,
    minHeight: Dp = Dimensions.HomeScreen.gridItemMinHeight
) {
    ItemContainer(
        modifier = modifier,
        enabled = enabled,
        onClick = onClick,
        minHeight = minHeight
    ) {
        ItemIcon(icon = icon)
        ItemContent(
            title = title,
            description = description
        )
    }
}

@Composable
private fun ItemContainer(
    modifier: Modifier = Modifier,
    enabled: Boolean,
    onClick: () -> Unit,
    minHeight: Dp,
    content: @Composable () -> Unit
) {
    AppCard(
        modifier = modifier
            .fillMaxSize()
            .shadowBluish(),
        enabled = enabled,
        onClick = onClick,
        regularColors = true
    ) {
        AppRow(
            modifier = Modifier
                .fillMaxSize()
                .heightIn(min = minHeight)
                .padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.CenterHorizontally),
            alignment = Alignment.CenterVertically
        ) {
            content()
        }
    }
}

@Composable
private fun ItemIcon(
    modifier: Modifier = Modifier,
    icon: DrawableResource
) {
    AppImage(
        modifier = modifier.size(100.dp),
        res = icon
    )
}

@Composable
private fun ItemContent(
    modifier: Modifier = Modifier,
    title: String,
    description: String
) {
    AppColumn(
        modifier = modifier
            .weight(1f)
            .padding(
                start = Dimensions.Paddings.screen.dp,
                end = Dimensions.Paddings.medium.dp
            ),
        alignment = Alignment.Start,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.CenterVertically)
    ) {
        ItemTitle(title = title)
        ItemDescription(description = description)
    }
}

@Composable
private fun ItemTitle(
    modifier: Modifier = Modifier,
    title: String
) {
    AppSubSectionTitleText(
        modifier = modifier,
        text = title,
        color = MaterialTheme.colorScheme.primary,
    )
}

@Composable
private fun ItemDescription(
    modifier: Modifier = Modifier,
    description: String
) {
    AppBodyText(
        modifier = modifier,
        text = description
    )
}

@Composable
internal fun HomeScreenGridChoiceItemSample() {
    HomeScreenGridChoiceItem(
        title = "المشاريع",
        description = "إدارة الأعمال و مصروفاتها و إيراداتها",
        icon = Res.drawable.ic_organization,
        onClick = {}
    )
}

@Preview
@Composable
fun HomeScreenGridChoiceItemPreview() {
    HomeScreenGridChoiceItemSample()
}

@Composable
internal fun NextArrowButton(
    modifier: Modifier = Modifier,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppIconImageViewButton(
        modifier = modifier,
        painter = rememberVectorPainter(image = Icons.AutoMirrored.Default.KeyboardArrowRight),
        enabled = enabled,
        onClick = onClick,
        shape = CircleShape,
        backgroundColor = Color.Transparent,
        tint = MaterialTheme.colorScheme.tertiary
    )
}