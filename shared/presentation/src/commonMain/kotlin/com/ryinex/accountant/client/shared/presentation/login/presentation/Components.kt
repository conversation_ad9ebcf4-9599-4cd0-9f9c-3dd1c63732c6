package com.ryinex.accountant.client.shared.presentation.login.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.Navigator
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.logo_word
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.domain.welcome.login.ViewModel
import com.ryinex.accountant.client.shared.domain.welcome.login.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppPasswordTextField
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppTextFieldDesign
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppDelayedConfirmButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.register.presentation.RegisterScreen
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import com.ryinex.accountant.client.resources.login_button
import com.ryinex.accountant.client.resources.login_change_organization
import com.ryinex.accountant.client.resources.login_organization_code_error
import com.ryinex.accountant.client.resources.login_organization_code_helper
import com.ryinex.accountant.client.resources.login_organization_code_hint
import com.ryinex.accountant.client.resources.login_organization_label
import com.ryinex.accountant.client.resources.login_password_error
import com.ryinex.accountant.client.resources.login_password_hint
import com.ryinex.accountant.client.resources.login_register_button
import com.ryinex.accountant.client.resources.login_username_error
import com.ryinex.accountant.client.resources.login_username_helper
import com.ryinex.accountant.client.resources.login_username_hint
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppVerticalLabeledBodyText


@Composable
internal fun AppLogo(modifier: Modifier = Modifier) {
    Box(modifier = modifier, contentAlignment = if (LocalIsDesktop.current) Alignment.CenterEnd else Alignment.Center) {
        Image(
            modifier = Modifier.width(256.dp),
            painter = painterResource(Res.drawable.logo_word),
            contentDescription = "Settings",
        )
    }
}

@Composable
internal fun UsernameField(
    modifier: Modifier = Modifier,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    registerUser: User?
) {
    AppTextField(
        state = screen.ui.username,
        modifier = modifier,
        hint = stringResource(Res.string.login_username_hint),
        enabled = true,
        helperText = if (registerUser != null) stringResource(Res.string.login_username_helper, registerUser.username) else "",
        errorText = stringResource(Res.string.login_username_error, screen.ui.usernameMinLength),
        design = AppTextFieldDesign.NormalTextField.Default.copy(singleLine = true)
    )
}

@Composable
internal fun PasswordField(
    modifier: Modifier = Modifier,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState
) {
    AppPasswordTextField(
        state = screen.ui.password,
        modifier = modifier,
        hint = stringResource(Res.string.login_password_hint),
        errorText = stringResource(Res.string.login_password_error, screen.ui.passwordMinLength),
        enabled = true,
        design = AppTextFieldDesign.NormalTextField.Default.copy(singleLine = true)
    )
}

@Composable
internal fun OrganizationCodeField(
    modifier: Modifier = Modifier,
    viewModel: ViewModel,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    state: ScreenState,
    registerOrganization: Organization?
) {
    val savedOrganization = state.savedOrganization
    if (savedOrganization == null || registerOrganization != null) {
        AppTextField(
            state = screen.ui.code,
            modifier = modifier,
            hint = stringResource(Res.string.login_organization_code_hint),
            enabled = true,
            helperText = if (registerOrganization != null) stringResource(Res.string.login_organization_code_helper, registerOrganization.organizationCode) else "",
            errorText = stringResource(Res.string.login_organization_code_error),
            design = AppTextFieldDesign.NormalTextField.Default.copy(singleLine = true)
        )
    } else {
        AppRow(modifier = modifier, arrangement = Arrangement.SpaceBetween, alignment = Alignment.CenterVertically) {
            AppVerticalLabeledBodyText(modifier = Modifier.weight(1f), text = savedOrganization.name, label = stringResource(Res.string.login_organization_label))
            AppDelayedConfirmButton(text = stringResource(Res.string.login_change_organization), enabled = true) { viewModel.changeSavedOrganization() }
        }
    }
}

@Composable
internal fun LoginButton(
    modifier: Modifier = Modifier,
    viewModel: ViewModel,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    state: ScreenState
) {
    AppButton(
        modifier = modifier,
        onClick = {
            viewModel.login(screen.ui.username.text.get(), screen.ui.password.text.get(), screen.ui.code.text.get())
        },
        enabled = screen.ui.isLoginEnabled.listen && !state.isLoading,
        text = stringResource(Res.string.login_button)
    )
}

@Composable
internal fun RegisterButton(
    modifier: Modifier = Modifier,
    navigator: Navigator?,
    state: ScreenState
) {
    AppTextButton(
        modifier = modifier,
        text = stringResource(Res.string.login_register_button),
        enabled = !state.isLoading,
        onClick = { navigator?.browserPush(RegisterScreen()) }
    )
}