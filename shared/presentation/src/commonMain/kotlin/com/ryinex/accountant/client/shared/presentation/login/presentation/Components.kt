package com.ryinex.accountant.client.shared.presentation.login.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.Navigator
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.logo_word
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.domain.welcome.login.ViewModel
import com.ryinex.accountant.client.shared.domain.welcome.login.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.AppPasswordTextField
import com.ryinex.accountant.client.shared.presentation.common.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.AppTextFieldDesign
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppDelayedConfirmButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextVertical
import com.ryinex.accountant.client.shared.presentation.register.presentation.RegisterScreen
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import org.jetbrains.compose.resources.painterResource


@Composable
internal fun RowScope.AppLogo() {
    Box(
        modifier = Modifier.Companion.weight(1f),
        contentAlignment = if (LocalIsDesktop.current) Alignment.CenterEnd else Alignment.Center
    ) {
        Image(
            modifier = Modifier.width(256.dp),
            painter = painterResource(Res.drawable.logo_word),
            contentDescription = "Settings",
        )
    }
}

@Composable
internal fun UsernameField(
    modifier: Modifier = Modifier,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    registerUser: User?
) {
    AppTextField(
        state = screen.ui.username,
        modifier = modifier,
        hint = "المستخدم",
        enabled = true,
        helperText = if (registerUser != null) "إسم المستخدم الخاص بك: ${registerUser.username}" else "",
        errorText = "المستخدم يجب أن يكون على الأقل ${screen.ui.usernameMinLength} أحرف",
        design = AppTextFieldDesign.NormalTextField.Default.copy(singleLine = true)
    )
}

@Composable
internal fun PasswordField(
    modifier: Modifier = Modifier,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState
) {
    AppPasswordTextField(
        state = screen.ui.password,
        modifier = modifier,
        hint = "كلمة المرور",
        errorText = "كلمة المرور يجب أن تكون على الأقل ${screen.ui.passwordMinLength} أحرف",
        enabled = true,
        design = AppTextFieldDesign.NormalTextField.Default.copy(singleLine = true)
    )
}

@Composable
internal fun OrganizationCodeField(
    modifier: Modifier = Modifier,
    viewModel: ViewModel,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    state: ScreenState,
    registerOrganization: Organization?
) {
    val savedOrganization = state.savedOrganization
    if (savedOrganization == null || registerOrganization != null) {
        AppTextField(
            state = screen.ui.code,
            modifier = modifier,
            hint = "كود المؤسسة",
            enabled = true,
            helperText = if (registerOrganization != null) "كود المؤسسة الخاص بك: ${registerOrganization.organizationCode}" else "",
            errorText = "كود المؤسسة يجب أن يكون على الأقل 1 حرف",
            design = AppTextFieldDesign.NormalTextField.Default.copy(singleLine = true)
        )
    } else {
        AppRow(modifier = modifier, arrangement = Arrangement.SpaceBetween, alignment = Alignment.CenterVertically) {
            AppLabeledBodyTextVertical(modifier = Modifier.weight(1f), text = savedOrganization.name, label = "المؤسسة")
            AppDelayedConfirmButton(text = "تغيير", enabled = true) { viewModel.changeSavedOrganization() }
        }
    }
}

@Composable
internal fun LoginButton(
    modifier: Modifier = Modifier,
    viewModel: ViewModel,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    state: ScreenState
) {
    AppButton(
        modifier = modifier,
        onClick = {
            viewModel.login(
                screen.ui.username.text.get(),
                screen.ui.password.text.get(),
                screen.ui.code.text.get()
            )
        },
        enabled = screen.ui.isLoginEnabled.listen && !state.isLoading,
        text = "تسجيل الدخول"
    )
}

@Composable
internal fun RegisterButton(
    modifier: Modifier = Modifier,
    navigator: Navigator?,
    state: ScreenState
) {
    AppTextButton(
        modifier = modifier,
        text = "تسجيل مؤسسة جديدة",
        enabled = !state.isLoading,
        onClick = { navigator?.browserPush(RegisterScreen()) }
    )
}