package com.ryinex.accountant.client.shared.presentation.login.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.logo_word
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import org.jetbrains.compose.resources.painterResource


@Composable
internal fun RowScope.AppLogo() {
    Box(
        modifier = Modifier.Companion.weight(1f),
        contentAlignment = if (LocalIsDesktop.current) Alignment.CenterEnd else Alignment.Center
    ) {
        Image(
            modifier = Modifier.width(256.dp),
            painter = painterResource(Res.drawable.logo_word),
            contentDescription = "Settings",
        )
    }
}