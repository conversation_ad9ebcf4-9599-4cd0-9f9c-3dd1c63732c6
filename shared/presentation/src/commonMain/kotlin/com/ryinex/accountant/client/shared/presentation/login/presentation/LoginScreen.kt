
package com.ryinex.accountant.client.shared.presentation.login.presentation

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.logo_word
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import cafe.adriel.voyager.navigator.Navigator
import com.ryinex.accountant.client.resources.ic_cancel
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.containers.AppFlowRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.home.presentation.ui.HomeScreen
import com.ryinex.accountant.client.shared.presentation.projects.presentation.ui.ProjectsScreen
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.presentation.utils.handleBackPress
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.domain.welcome.login.ViewModel
import com.ryinex.accountant.client.shared.domain.welcome.login.models.ScreenState
import com.ryinex.accountant.client.shared.domain.welcome.login.models.SideEffect
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions.Paddings.screen
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource

class LoginScreen(private val registerUser: User?, private val registerOrganization: Organization?) : BrowserScreen {

    override val urlSegment: String = "login"

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current

        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                users = TempDI.users,
                auth = TempDI.auth,
                logger = TempDI.logger,
                organization = TempDI.organizations,
                errorHandler = TempDI.errorHandler,
                coroutinesContext = emptyCoroutineContext
            )
        }

        LaunchedEffect(Unit) {
            handleBackPress(navigator)

            if (viewModel.isLoggedIn() && registerUser == null) {
                navigator?.browserPush(HomeScreen())
            } else {
                viewModel.sideEffects.stream.collect {
                    when (it) {
                        is SideEffect.LoginSuccess -> {
                            handleLoginSuccess(navigator, it.user)
                        }
                    }
                }
            }
        }

        val state = viewModel.screenState.stream.collectAsState(Samples.initState).value
        val screen = remember { ScreenState() }

        AppBasePage(
            title = "تسجيل الدخول",
            isPullToRefreshEnabled = false,
            isPullToRefresh = false,
            onPullToRefresh = { },
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
        ) {
            val scope = rememberCoroutineScope()
            LoginScreenContent(
                viewModel = viewModel,
                state = state,
                registerUser = registerUser,
                registerOrganization = registerOrganization,
                screen = screen
            )

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }
}

internal fun handleLoginSuccess(navigator: Navigator?, user: User) {
    if (user.isAdmin) {
        navigator?.browserPush(HomeScreen(), replace = true)
    } else {
        navigator?.browserPush(HomeScreen(), replace = true)
    }
}


@Composable
private fun LoginScreenContent(
    modifier: Modifier = Modifier,
    viewModel: ViewModel,
    state: ScreenState,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    registerUser: User?,
    registerOrganization: Organization?
) {
    AppFlowRow(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState()),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.CenterVertically),
        alignment = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.CenterHorizontally)
    ) {
        AppLogo()

        Box(
            modifier = Modifier.weight(1f),
            contentAlignment = if (LocalIsDesktop.current) Alignment.CenterStart else Alignment.Center
        ) {
            LoginForm(
                modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.5f) else Modifier,
                viewModel = viewModel,
                state = state,
                registerUser = registerUser,
                registerOrganization = registerOrganization,
                screen = screen
            )
        }
    }
}

@Composable
private fun LoginForm(
    modifier: Modifier = Modifier,
    screen: com.ryinex.accountant.client.shared.presentation.login.presentation.ScreenState,
    viewModel: ViewModel,
    state: ScreenState,
    registerUser: User?,
    registerOrganization: Organization?
) {
    val navigator = LocalNavigator.current

    // Initialize code field based on saved organization or register organization
    LaunchedEffect(state.savedOrganization, registerOrganization) {
        val initialCode = if (registerOrganization == null) {
            state.savedOrganization?.organizationCode ?: ""
        } else {
            ""
        }
        screen.ui.code.text.update(initialCode)
    }

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.CenterVertically),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        UsernameField(
            modifier = Modifier.fillMaxWidth(),
            screen = screen,
            registerUser = registerUser
        )

        PasswordField(
            modifier = Modifier.fillMaxWidth(),
            screen = screen
        )

        OrganizationCodeField(
            modifier = Modifier.fillMaxWidth(),
            viewModel = viewModel,
            screen = screen,
            state = state,
            registerOrganization = registerOrganization
        )

        LoginButton(
            modifier = Modifier.fillMaxWidth(),
            viewModel = viewModel,
            screen = screen,
            state = state
        )

        RegisterButton(
            modifier = Modifier.fillMaxWidth(),
            navigator = navigator,
            state = state
        )
    }
}