package com.ryinex.accountant.client.shared.presentation.login.presentation

import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppTextFieldState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import core.common.status.Status
import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map

class ScreenState(
    status: StatusRepository,
    val ui: UIState = UIState(status = status)
)

class UIState(
    status: StatusRepository
) {
    val passwordMinLength = 6
    val password = AppTextFieldState { it.map { it.trim().length < passwordMinLength }}

    val usernameMinLength = 3
    val username = AppTextFieldState  { it.map { it.trim().length < usernameMinLength }}

    val code = AppTextFieldState { it.map { it.trim().length < 1 } }
    val status = DerivedState(status.stream.value, status.stream.map { it.filter { it !is Status.Success } })

    private val loginEnabledFlow = combine(
        username.error.stream, password.error.stream, code.error.stream
    ) { usernameError, passwordError, codeError ->
        !usernameError && !passwordError && !codeError
    }
    val isLoginEnabled = DerivedState(false, loginEnabledFlow)
}