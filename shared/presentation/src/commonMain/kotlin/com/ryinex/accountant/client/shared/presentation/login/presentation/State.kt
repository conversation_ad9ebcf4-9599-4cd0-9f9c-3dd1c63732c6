package com.ryinex.accountant.client.shared.presentation.login.presentation

import com.ryinex.accountant.client.shared.presentation.common.AppTextFieldState
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map

class ScreenState(
    val ui: UIState = UIState()
)

class UIState {
    val passwordMinLength = 6
    val password = AppTextFieldState { it.map { it.trim().length < passwordMinLength }}

    val usernameMinLength = 3
    val username = AppTextFieldState  { it.map { it.trim().length < usernameMinLength }}

    val code = AppTextFieldState { it.map { it.trim().length < 1 } }

    private val loginEnabledFlow = combine(
        username.error.stream, password.error.stream, code.error.stream
    ) { usernameError, passwordError, codeError ->
        !usernameError && !passwordError && !codeError
    }
    val isLoginEnabled = DerivedState(false, loginEnabledFlow)
}