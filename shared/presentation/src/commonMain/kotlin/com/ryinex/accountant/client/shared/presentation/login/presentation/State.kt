package com.ryinex.accountant.client.shared.presentation.login.presentation

import com.ryinex.accountant.client.shared.presentation.common.AppTextFieldState
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import kotlinx.coroutines.flow.map

class ScreenState(
    val ui: UIState = UIState()
)

class UIState {
    private val passwordText = StringState("")
    private val isPasswordError = DerivedState(false, passwordText.stream.map { it.trim().length < 6 })
    val password: AppTextFieldState = AppTextFieldState(text = passwordText, error = isPasswordError)
}