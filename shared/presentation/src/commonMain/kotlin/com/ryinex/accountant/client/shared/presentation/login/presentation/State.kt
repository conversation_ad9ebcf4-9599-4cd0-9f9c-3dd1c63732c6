package com.ryinex.accountant.client.shared.presentation.login.presentation

import com.ryinex.accountant.client.shared.presentation.common.AppTextFieldState
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import kotlinx.coroutines.flow.map

class ScreenState(
    val ui: UIState = UIState()
)

class UIState {
    private val passwordText = StringState("")
    private val isPasswordError = DerivedState(false, passwordText.stream.map { it.trim().length < 6 })
    val password: AppTextFieldState = AppTextFieldState(text = passwordText, error = isPasswordError)

    private val usernameText = StringState("")
    private val isUsernameError = DerivedState(false, usernameText.stream.map { it.trim().length < 3 })
    val username: AppTextFieldState = AppTextFieldState(text = usernameText, error = isUsernameError)

    private val codeText = StringState("")
    private val isCodeError = DerivedState(false, codeText.stream.map { it.isBlank() })
    val code: AppTextFieldState = AppTextFieldState(text = codeText, error = isCodeError)
}