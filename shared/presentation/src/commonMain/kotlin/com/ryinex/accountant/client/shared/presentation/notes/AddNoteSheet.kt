package com.ryinex.accountant.client.shared.presentation.notes

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.data.common.error.ErrorHandler
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.notes.models.Note
import com.ryinex.accountant.client.shared.data.notes.repositories.NotesRepository
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppDelayedConfirmButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddNoteSheet(
    modifier: Modifier = Modifier,
    initialNote: Note?,
    isEditing: Boolean,
    beneficiary: Beneficiary?,
    onFinishAdd: () -> Unit,
    onDismissRequest: () -> Unit
) {
    val minNoteLength = remember { 3 }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }

    AppBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismissRequest
    ) {
        var note by remember { mutableStateOf(initialNote?.note ?: "") }

        val isNoteError by remember(note) { mutableStateOf(note.trim().length < minNoteLength) }

        val isEnabled by remember(isNoteError, isLoading) {
            mutableStateOf(!isNoteError && !isLoading)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = if (isEditing) "تعديل ملاحظة" else "إضافة ملاحظة",
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = if (isEditing) "تعديل" else "إضافة",
                    enabled = isEnabled,
                    onClick = {
                        scope.launch {
                            isLoading = true

                            if (!isEditing) {
                                addNote(
                                    note = note,
                                    beneficiaryId = beneficiary?.id,
                                    customerId = null,
                                    projectId = null,
                                    onError = {
                                        isLoading = false
                                        onDismissRequest()
                                    }
                                )
                            } else {
                                editNote(
                                    noteId = initialNote!!.id,
                                    note = note,
                                    beneficiaryId = beneficiary?.id,
                                    customerId = null,
                                    projectId = null,
                                    version = initialNote.version,
                                    onError = {
                                        isLoading = false
                                        onDismissRequest()
                                    }
                                )
                            }

                            isLoading = false
                            onFinishAdd()
                            onDismissRequest()
                        }
                    }
                )
            }

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = note,
                hint = "الملاحظة",
                enabled = true,
                isError = isNoteError,
                onValueChange = { note = it },
                errorText = "الملاحظة يجب أن تكون على الأقل $minNoteLength أحرف"
            )

            if (isEditing) {
                Box(modifier = Modifier.fillMaxWidth()) {
                    AppDelayedConfirmButton(
                        modifier = Modifier.align(Alignment.CenterEnd),
                        text = "حذف الملاحظة",
                        enabled = !isLoading,
                        containerColor = MaterialTheme.colorScheme.error,
                        onClick = {
                            scope.launch {
                                isLoading = true

                                deleteNote(
                                    noteId = initialNote!!.id,
                                    onError = {
                                        isLoading = false
                                        onDismissRequest()
                                    }
                                )

                                isLoading = false
                                onFinishAdd()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }
        }
    }
}

private suspend fun addNote(
    note: String,
    beneficiaryId: Long?,
    customerId: Long?,
    projectId: Long?,
    notes: NotesRepository = TempDI.notes,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري إضافة ملاحظة ..."),
            success = Message.fromString("تم إضافة ملاحظة"),
            job = {
                notes.create(note = note, beneficiaryId = beneficiaryId, customerId = customerId, projectId = projectId)
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun editNote(
    noteId: Long,
    note: String,
    beneficiaryId: Long?,
    customerId: Long?,
    projectId: Long?,
    version: Long,
    notes: NotesRepository = TempDI.notes,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري تعديل ملاحظة ..."),
            success = Message.fromString("تم تعديل ملاحظة"),
            job = {
                notes.edit(
                    noteId = noteId,
                    note = note,
                    beneficiaryId = beneficiaryId,
                    customerId = customerId,
                    projectId = projectId,
                    version = version
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun deleteNote(
    noteId: Long,
    notes: NotesRepository = TempDI.notes,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري حذف ملاحظة ..."),
            success = Message.fromString("تم حذف ملاحظة"),
            job = {
                notes.delete(noteId = noteId)
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}