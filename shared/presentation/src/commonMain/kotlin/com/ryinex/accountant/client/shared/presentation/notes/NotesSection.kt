package com.ryinex.accountant.client.shared.presentation.notes

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_edit
import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.data.notes.models.Note
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppGrid
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppExpandedIconButtonView
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import core.common.extensions.dateFormat
import org.jetbrains.compose.resources.painterResource

@Composable
internal fun NotesSection(
    notes: List<Note>,
    isEditEnabled: Boolean,
    beneficiary: Beneficiary?,
    onFinishEdit: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isDesktop = LocalIsDesktop.current
    var isExpanded by remember { mutableStateOf(false) }
    val filteredNotes: List<Note> = remember(isExpanded, isDesktop, notes) {
        if (!isExpanded && isDesktop) {
            notes.take(Dimensions.Cards.maxCardsDesktopCount)
        } else if (!isExpanded) {
            notes.take(1)
        } else notes
    }

    AppSurface(
        modifier = modifier.fillMaxWidth().shadowBluish(),
        padding = PaddingValues(Dimensions.Paddings.medium.dp),
        clickable = true,
        onClick = { isExpanded = !isExpanded }
    ) {
        AppColumn(
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.Start
        ) {
            AppRow(
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.weight(1f))

                AppExpandedIconButtonView(
                    isExpanded = isExpanded,
                    onClick = { isExpanded = !isExpanded },
                    enabled = true
                )
            }

            AppGrid(
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                alignment = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                maxItems = if (isDesktop) Dimensions.Cards.maxCardsDesktopCount else 1
            ) {
                filteredNotes.forEach {
                    Note(
                        note = it,
                        modifier = Modifier.adaptWidth(),
                        beneficiary = beneficiary,
                        isEditEnabled = isEditEnabled,
                        onFinishEdit = onFinishEdit
                    )
                }
            }
        }
    }
}

@Composable
private fun Note(
    note: Note,
    beneficiary: Beneficiary?,
    isEditEnabled: Boolean,
    onFinishEdit: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    var showEditSheet by remember { mutableStateOf(false) }

    AppCard(
        modifier = modifier.fillMaxWidth(),
        padding = Dimensions.Paddings.medium.dp,
        enabled = true,
        onClick = { isExpanded = !isExpanded },
        regularColors = true
    ) {
        AppColumn(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.Start
        ) {
            AppRow(
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                AppBodyText(
                    modifier = Modifier.weight(1f),
                    text = note.note,
                )

                AppSupportImageViewButton(
                    painter = painterResource(Res.drawable.ic_edit),
                    backgroundColor = MaterialTheme.colorScheme.tertiary,
                    enabled = isEditEnabled,
                    onClick = { showEditSheet = !showEditSheet }
                )
                AppExpandedIconButtonView(
                    isExpanded = isExpanded,
                    onClick = { isExpanded = !isExpanded },
                    enabled = true
                )
            }

            if (isExpanded) {
                AppLabeledBodyTextHorizontal(
                    label = "بواسطة",
                    text = note.createdByUser.name
                )

                AppLabeledBodyTextHorizontal(
                    label = "بتاريخ",
                    text = note.createdAt.dateFormat(),
                    ltrText = true
                )
            }
        }
    }

    if (showEditSheet) {
        AddNoteSheet(
            initialNote = note,
            isEditing = true,
            onDismissRequest = { showEditSheet = false },
            onFinishAdd = onFinishEdit,
            beneficiary = beneficiary
        )
    }
}