package com.ryinex.accountant.client.shared.presentation.organization.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText

@Composable
internal fun OrganizationDetailsGridItem(
    modifier: Modifier = Modifier,
    title: String,
    value: String,
    minHeight: Dp = 0.dp
) {
    AppColumn(
        modifier = modifier
            .heightIn(min = minHeight)
            .widthIn(min = minHeight)
            .padding(Dimensions.Paddings.small.dp),
        alignment = Alignment.Start,
        arrangement = Arrangement.Top
    ) {
        AppLabelText(
            text = title,
        )

        AppBodyText(
            text = value,
        )
    }
}

//@Composable
//internal fun OrganizationDetailsGridItem(
//    modifier: Modifier = Modifier,
//    title: String,
//    value: String,
//    minHeight: Dp = 0.dp
//) {
//    Card(
//        modifier = modifier
//    ) {
//        AppColumn(
//            modifier = Modifier
//                .heightIn(min = minHeight)
//                .widthIn(min = minHeight)
//                .padding(Dimensions.Paddings.small.dp),
//            alignment = Alignment.CenterHorizontally,
//            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.CenterVertically)
//        ) {
//            AppBodyText(
//                text = title,
//            )
//
//            AppBodyText(
//                text = value,
//            )
//        }
//    }
//}