package com.ryinex.accountant.client.shared.presentation.organization.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppGrid
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.projects.presentation.ui.GridItem
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.domain.organization.ViewModel
import com.ryinex.accountant.client.shared.domain.organization.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.tables.capitalTransactionsTable
import kotlinx.coroutines.launch

class OrganizationScreen : BrowserScreen {

    override val urlSegment: String = "organization"

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(TempDI.status, TempDI.organizations, TempDI.capitalMoney, emptyCoroutineContext)
        }

        LaunchedEffect(Unit) { viewModel.initScreen() }
        val state = viewModel.screenState.stream.collectAsState(Samples.initState).value

        AppBasePage(
            title = "المؤسسة",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = state.isBackEnabled,
            isPullToRefreshEnabled = true,
            isPullToRefresh = !state.isBackEnabled,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = {
                navigator?.browserPop()
            }
        ) {
            val scope = rememberCoroutineScope()

            OrganizationScreenContent(
                state = state,
                viewModel = viewModel
            )

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }
}

@Composable
private fun OrganizationScreenContent(
    modifier: Modifier = Modifier,
    state: ScreenState,
    viewModel: ViewModel
) {
    val isDesktop = LocalIsDesktop.current
    val lazyState = rememberLazyListState()
    val table = capitalTransactionsTable(lazyState, state.transactions)

    AppLazyColumn(
        modifier = modifier
            .fillMaxSize(),
        lazyState = lazyState,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
        alignment = Alignment.Start
    ) { horizontalScrollState ->

        item {
            AppSurface(
                modifier = Modifier.sectionsSpacing().shadowBluish(),
                padding = PaddingValues(Dimensions.Paddings.medium.dp),
            ) {
                AppColumn(
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                    alignment = Alignment.Start
                ) {
                    AppRow(
                        modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
                        arrangement = Arrangement.SpaceBetween,
                        alignment = Alignment.CenterVertically
                    ) {

                        Box(
                            modifier = Modifier.fillMaxHeight().weight(1f),
                            contentAlignment = Alignment.CenterStart
                        ) {
                            AppSectionTitleText(
                                state.organization?.name ?: "",
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

//                    if (state.isEditProjectEnabled) {
//                        AppTextButton(
//                            modifier = Modifier,
//                            text = "تعديل",
//                            enabled = state.isEditProjectEnabled,
//                            iconPainter = rememberVectorPainter(Icons.Default.Edit),
//                            onClick = { showEditSheet = true }
//                        )
//                    }
                    }

                    AppLabeledBodyTextHorizontal(label = "الوصف", text = state.organization?.description ?: "")
                    AppLabeledBodyTextHorizontal(label = "كود المؤسسة", text = state.organization?.organizationCode ?: "")
                }
            }
        }


        item {
            AppGrid {

                GridItem(
                    modifier = Modifier.adaptWidth().weight(1f),
                    top = "الإيرادات",
                    bottom = state.organization?.paidIncomes?.formatted() ?: "",
                    enabled = false,
                    onClick = { }
                )

                GridItem(
                    modifier = Modifier.adaptWidth().weight(1f),
                    top = "مصاريف",
                    bottom = state.organization?.expenses?.formatted() ?: "",
                    enabled = false,
                    onClick = { }
                )

                GridItem(
                    modifier = Modifier.adaptWidth().weight(1f),
                    top = "رأس المال",
                    bottom = state.organization?.capital?.formatted() ?: "",
                    enabled = false,
                    onClick = { }
                )

                GridItem(
                    modifier = Modifier.adaptWidth().weight(1f),
                    top = "المتاح",
                    bottom = state.organization?.totalCapital?.formatted() ?: "",
                    enabled = false,
                    onClick = { }
                )

                GridItem(
                    modifier = Modifier.adaptWidth().weight(1f),
                    top = "متبقى إيرادات",
                    bottom = state.organization?.remainingIncomes?.formatted() ?: "",
                    enabled = false,
                    onClick = { }
                )
            }
        }


        item {
            HorizontalDivider()
        }

        OrganizationCapitalSection(
            state = state,
            viewModel = viewModel,
            isDesktop = isDesktop,
            scrollableState = horizontalScrollState,
            table = table
        )
    }
}