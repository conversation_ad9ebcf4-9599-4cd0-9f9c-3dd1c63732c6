package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.ui.Modifier
import com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui.BeneficiaryTransactionsListSection
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.domain.projects.expenses.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.expenses.models.ScreenState
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.kotlin.datatable.data.DataTable

fun LazyListScope.ProjectExpensesList(
    table: DataTable<ProjectExpense>,
    scrollState: ScrollState,
    state: ScreenState,
    viewModel: ViewModel,
    isDesktop: Boolean
) {
    item {
        AppTextField(
            modifier = Modifier.fillMaxWidth(),
            text = state.search,
            hint = "بحث",
            enabled = true,
            isError = false,
            onValueChange = viewModel::updateSearch,
            errorText = ""
        )
    }


    BeneficiaryTransactionsListSection(
        table = table,
        transactions = state.filteredExpense,
        isPaymentEnabled = false,
        isEditEnabled = state.isExpensesEditEnabled,
        isDesktop = isDesktop,
        onRefresh = { viewModel.refreshScreen() },
        scrollState = scrollState
    )
}
