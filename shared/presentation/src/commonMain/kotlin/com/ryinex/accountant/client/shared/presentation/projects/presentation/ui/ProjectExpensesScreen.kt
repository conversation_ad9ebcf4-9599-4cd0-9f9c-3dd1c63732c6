package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_add
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.resources.ic_export
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppFAB
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.domain.projects.expenses.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.expenses.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.tables.projectExpenseTable
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource

class ProjectExpensesScreen(
    private val project: Project
) : BrowserScreen {

    override val urlSegment: String = "expenses"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                projects = TempDI.projects,
                termsGroups = TempDI.termsGroups,
                terms = TempDI.terms,
                beneficiaries = TempDI.beneficiaries,
                users = TempDI.users,
                coroutinesContext = emptyCoroutineContext
            )
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initExpensesState).value
        var showAddSheet by remember { mutableStateOf(false) }
        LaunchedEffect(Unit) { viewModel.initScreen(project) }

        AppBasePage(
            title = "الأعمال",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = true,
            isPullToRefreshEnabled = true,
            isPullToRefresh = !state.isBackEnabled,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = { navigator?.browserPop() },
            fab = {
                AddFab(
                    isEnabled = state.isExpensesAddEnabled,
                    onClick = { showAddSheet = true }
                )
            }
        ) {
            val scope = rememberCoroutineScope()

            ExpensesScreenContent(
                state = state,
                viewModel = viewModel
            )

            if (showAddSheet) {
                AddSheet(
                    state = state,
                    viewModel = viewModel,
                    onDismissRequest = { showAddSheet = false }
                )
            }

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }

    @Composable
    private fun ExpensesScreenContent(
        modifier: Modifier = Modifier,
        state: ScreenState,
        viewModel: ViewModel
    ) {
        val lazyState = rememberLazyListState()
        var expense by remember { mutableStateOf<ProjectExpense?>(null) }
        val table = projectExpenseTable(
            lazyState = lazyState,
            items = state.filteredExpense,
            isEditEnabled = state.isExpensesEditEnabled,
            onEdit = { item -> expense = item }
        )

        val isDesktop = LocalIsDesktop.current
        var searchValue by remember { mutableStateOf("") }

        AppLazyColumn(
            modifier = modifier
                .fillMaxSize(),
            lazyState = lazyState,
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.Start
        ) { horizontalScrollState ->

            item {
                AppSurface(
                    modifier = Modifier.sectionsSpacing().shadowBluish(),
                    padding = PaddingValues(Dimensions.Paddings.medium.dp),
                ) {
                    AppColumn(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                        alignment = Alignment.Start
                    ) {
                        AppRow(
                            modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
                            arrangement = Arrangement.SpaceBetween,
                            alignment = Alignment.CenterVertically
                        ) {

                            AppSubSectionTitleText(state.project?.name ?: "", color = MaterialTheme.colorScheme.primary)
                        }

                        AppLabeledBodyTextHorizontal(label = "الوصف", text = state.project?.description ?: "")

                        AppLabeledBodyTextHorizontal(label = "العميل", text = state.project?.customer?.name ?: "")
                    }
                }
            }

            item {
                var showAddSheet by remember { mutableStateOf(false) }

                AppRow(
                    modifier = Modifier.fillMaxWidth().sectionsSpacing(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {

                    AppSectionTitleText(
                        modifier = Modifier.weight(1f),
                        text = "المصاريف"
                    )

                    AppColumn(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                        alignment = Alignment.Start
                    ) {
                        AppTextButton(
                            modifier = Modifier,
                            text = "إضافة",
                            enabled = state.isExpensesAddEnabled,
                            onClick = { showAddSheet = true },
                            iconPainter = painterResource(Res.drawable.ic_add)
                        )

                        AppTextButton(
                            modifier = Modifier,
                            text = "إستخراج",
                            enabled = state.isExpensesAddEnabled,
                            onClick = { viewModel.extractExpenses(state.filteredExpense) },
                            iconPainter = painterResource(Res.drawable.ic_export),
                            iconMirror = true
                        )
                    }
                }

                if (showAddSheet) {
                    AddSheet(
                        state = state,
                        viewModel = viewModel,
                        onDismissRequest = { showAddSheet = false }
                    )
                }
            }

            ProjectExpensesList(
                table = table,
                scrollState = horizontalScrollState,
                state = state,
                viewModel = viewModel,
                isDesktop = isDesktop
            )

            item {
                Spacer(modifier = Modifier.sectionsSpacing())
            }
        }

        if (expense != null) {

            ProjectExpenseSheet(
                project = expense!!.project,
                expense = expense,
                onDismissRequest = { expense = null },
                onFinish = {
                    expense = null
                    viewModel.refreshScreen()
                }
            )
        }
    }

    @Composable
    private fun AddSheet(
        state: ScreenState,
        viewModel: ViewModel,
        onDismissRequest: () -> Unit
    ) {
        ProjectExpenseSheet(
            project = state.project,
            expense = null,
            onDismissRequest = onDismissRequest,
            onFinish = {
                onDismissRequest()
                viewModel.refreshScreen()
            }
        )
    }

    @Composable
    private fun AddFab(
        isEnabled: Boolean,
        onClick: () -> Unit
    ) {
        AppFAB(
            enabled = isEnabled,
            onClick = onClick,
            icon = painterResource(Res.drawable.ic_add)
        )
    }
}

