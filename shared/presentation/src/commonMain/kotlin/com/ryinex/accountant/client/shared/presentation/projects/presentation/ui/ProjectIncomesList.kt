package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_minus
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.data.customers.models.Customer
import com.ryinex.accountant.client.shared.domain.projects.incomes.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.incomes.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.app
import com.ryinex.accountant.client.shared.data.common.utilities.formattedMinimum
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledTextVertical
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import kotlinx.datetime.Clock
import org.jetbrains.compose.resources.painterResource

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AddIncomeSheet(
    state: ScreenState,
    viewModel: ViewModel,
    onDismissRequest: () -> Unit
) {
    val minDescriptionLength = remember { 3 }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var amount by remember { mutableStateOf(0.0.app()) }
        var descriptionText by remember { mutableStateOf("") }
        var customerText by remember { mutableStateOf("") }
        var customer by remember { mutableStateOf<Customer?>(state.project?.customer) }
        var remainingCount by remember { mutableStateOf(1) }
        val remaining = remember { mutableStateOf(listOf<Triple<Int, AppDouble, String>>()) }
        val total = remember(
            amount,
            remaining.value
        ) { amount + remaining.value.fold(0.0.app()) { acc, item -> acc + item.second } }
        val description by remember(remaining.value, descriptionText) {
            mutableStateOf("$descriptionText\n${remaining.value.joinToString("\n") { it.third }}")
        }
        val isAmountError by remember(amount) { mutableStateOf(amount == 0.0.app()) }
        val isDescriptionError by remember(descriptionText) { mutableStateOf(descriptionText.trim().length < minDescriptionLength) }

        val isEnabled by remember(
            isAmountError,
            isDescriptionError,
            customer,
            remaining.value
        ) {
            val remainingCorrect = remaining.value.all { it.second > 0.0.app() }
            mutableStateOf(!isAmountError && !isDescriptionError && customer != null && remainingCorrect)
        }

        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            item {
                AppSectionTitleText("إضافة إيرادات")
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppTextButton(
                        modifier = Modifier,
                        text = "إلغاء",
                        enabled = true,
                        onClick = { onDismissRequest() }
                    )

                    AppTextButton(
                        modifier = Modifier,
                        text = "إضافة",
                        enabled = isEnabled,
                        onClick = {
                            viewModel.addIncome(
                                customerId = customer!!.id,
                                amount = total,
                                description = description,
                                transactionDateIseoString = Clock.System.now().toString(),
                            )
                            onDismissRequest()
                        },
                    )
                }
            }

            item {
                AppDecimalTextField(
                    modifier = Modifier.fillMaxWidth(),
                    amount = amount,
                    hint = "القيمة",
                    enabled = true,
                    isError = isAmountError,
                    onlyPositive = true,
                    onValueChange = { amount = it },
                )
            }

            item {
                AppTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = descriptionText,
                    hint = "ملاحظات",
                    enabled = true,
                    isError = isDescriptionError,
                    onValueChange = { descriptionText = it },
                    errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف"
                )
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {
                    AppSubSectionTitleText("متبقى")

                    AppSupportImageViewButton(
                        painter = rememberVectorPainter(Icons.Default.Add),
                        enabled = true,
                        onClick = {
                            remaining.value += (Triple(++remainingCount, 0.0.app(), ""))
                        }
                    )
                }
            }

            itemsIndexed(remaining.value, { index, item -> item.first }) { index, item ->
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                    alignment = Alignment.CenterVertically
                ) {
                    AppColumn(
                        modifier = Modifier.weight(1f),
                        arrangement = Arrangement.Top,
                        alignment = Alignment.Start,
                    ) {
                        AppDecimalTextField(
                            modifier = Modifier.fillMaxWidth(),
                            amount = item.second,
                            hint = "متبقى",
                            enabled = true,
                            isError = item == 0.0.app(),
                            onlyPositive = true,
                            onValueChange = {
                                remaining.value = remaining.value.toMutableList().apply {
                                    set(index, Triple(item.first, it, "متبقى ${it.formatted()} مقابل "))
                                }
                            },
                        )

                        AppTextField(
                            modifier = Modifier.fillMaxWidth(),
                            text = item.third,
                            hint = "ملاحظات",
                            enabled = true,
                            isError = false,
                            onValueChange = {
                                remaining.value = remaining.value.toMutableList().apply {
                                    set(index, Triple(item.first, item.second, it))
                                }
                            },
                            errorText = ""
                        )
                    }

                    AppSupportImageViewButton(
                        painter = painterResource(Res.drawable.ic_minus),
                        enabled = true,
                        onClick = { remaining.value = remaining.value.toMutableList().apply { remove(item) } }
                    )
                }

            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {
                    AppSubSectionTitleText("الإجمالي")
                    AppSubSectionTitleText(total.formattedMinimum(), color = MaterialTheme.colorScheme.tertiary)
                }
            }

            item {
                AppLabeledTextVertical(label = "الملاحظات", text = description)
            }

            item {
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = customerText,
                    hint = "العميل",
                    enabled = true,
                    isError = false,
                    minSuggestLength = 0,
                    errorText = "",
                    allItems = state.customers,
                    searchMapper = { it.name },
                    displayMapper = { it.name },
                    onItemClick = { customer = it },
                    onEmptyItemClick = { customer = null },
                    items = state.customers,
                    onValueChange = { customerText = it },
                    selected = customer,
                    onCancelSelected = { customer = null },
                    isCancelSelectedEnabled = false
                )
            }
        }
    }
}