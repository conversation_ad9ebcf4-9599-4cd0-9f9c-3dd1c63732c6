package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_add
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppFAB
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.domain.projects.incomes.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.incomes.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.projects.models.ProjectIncome
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.tables.customersTransactionsTable
import com.ryinex.accountant.client.shared.presentation.customers.presentation.ui.CustomersTransactionsListSection
import com.ryinex.accountant.client.shared.presentation.customers.presentation.ui.CustomersTransactionsListSectionBottomSheets
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource

class ProjectIncomesScreen(
    private val project: Project
) : BrowserScreen {

    override val urlSegment: String = "incomes"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                projects = TempDI.projects,
                users = TempDI.users,
                customers = TempDI.customers,
                transactions = TempDI.incomes,
                coroutinesContext = emptyCoroutineContext
            )
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initIncomesState).value
        var showAddSheet by remember { mutableStateOf(false) }
        LaunchedEffect(Unit) { viewModel.initScreen(project) }

        AppBasePage(
            title = "الأعمال",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = true,
            isPullToRefreshEnabled = true,
            isPullToRefresh = !state.isBackEnabled,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = { navigator?.browserPop() },
            fab = {
                AddFab(
                    isEnabled = state.isIncomesAddEnabled,
                    onClick = { showAddSheet = true }
                )
            },
        ) {
            val scope = rememberCoroutineScope()

            IncomesScreenContent(
                state = state,
                viewModel = viewModel
            )

            if (showAddSheet) {
                AddSheet(
                    state = state,
                    viewModel = viewModel,
                    onDismissRequest = { showAddSheet = false }
                )
            }

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }

    @Composable
    private fun IncomesScreenContent(
        modifier: Modifier = Modifier,
        state: ScreenState,
        viewModel: ViewModel
    ) {
        val isDesktop = LocalIsDesktop.current
        val payIncome = remember { mutableStateOf<ProjectIncome?>(null) }
        val editIncome = remember { mutableStateOf<ProjectIncome?>(null) }
        val lazyState = rememberLazyListState()
        val table = customersTransactionsTable(
            lazyState = lazyState,
            items = state.incomes,
            isEditEnabled = state.isBackEnabled,
            isPaymentEnabled = state.isEditEnabled,
            onPay = { item -> payIncome.value = item },
            onEdit = { item -> editIncome.value = item }
        )
        AppLazyColumn(
            modifier = modifier
                .fillMaxSize(),
            lazyState = lazyState,
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.Start
        ) { horizontalScrollState ->


            item {
                AppSurface(
                    modifier = Modifier.sectionsSpacing().shadowBluish(),
                    padding = PaddingValues(Dimensions.Paddings.medium.dp),
                ) {
                    AppColumn(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                        alignment = Alignment.Start
                    ) {
                        AppRow(
                            modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
                            arrangement = Arrangement.SpaceBetween,
                            alignment = Alignment.CenterVertically
                        ) {

                            AppSubSectionTitleText(state.project?.name ?: "", color = MaterialTheme.colorScheme.primary)
                        }

                        AppLabeledBodyTextHorizontal(label = "الوصف", text = state.project?.description ?: "")

                        AppLabeledBodyTextHorizontal(label = "العميل", text = state.project?.customer?.name ?: "")
                    }
                }
            }

            item {
                var searchValue by remember { mutableStateOf("") }
                var showAddSheet by remember { mutableStateOf(false) }

                AppRow(
                    modifier = Modifier.fillMaxWidth().sectionsSpacing(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {

                    AppSectionTitleText(
                        modifier = Modifier.weight(1f),
                        text = "الإيرادات"
                    )

                    AppTextButton(
                        modifier = Modifier,
                        text = "إضافة",
                        enabled = state.isIncomesAddEnabled,
                        onClick = { showAddSheet = true },
                        iconPainter = painterResource(Res.drawable.ic_add)
                    )
                }

                if (showAddSheet) {
                    AddSheet(
                        state = state,
                        viewModel = viewModel,
                        onDismissRequest = { showAddSheet = false }
                    )
                }
            }

            CustomersTransactionsListSection(
                table = table,
                scrollState = horizontalScrollState,
                transactions = state.incomes,
                isPaymentEnabled = true,
                isEditEnabled = state.isEditEnabled,
                isDesktop = isDesktop,
                onFinishPayment = { viewModel.refreshScreen() },
                onRefresh = { viewModel.refreshScreen() },
            )
        }

        CustomersTransactionsListSectionBottomSheets(
            payIncome = payIncome,
            editIncome = editIncome,
            onFinishPayment = { viewModel.refreshScreen() }
        )
    }

    @Composable
    private fun AddSheet(
        state: ScreenState,
        viewModel: ViewModel,
        onDismissRequest: () -> Unit
    ) {
        AddIncomeSheet(
            state = state,
            viewModel = viewModel,
            onDismissRequest = onDismissRequest
        )
    }

    @Composable
    private fun AddFab(
        isEnabled: Boolean,
        onClick: () -> Unit
    ) {
        AppFAB(
            enabled = isEnabled,
            onClick = onClick,
            icon = painterResource(Res.drawable.ic_add)
        )
    }
}