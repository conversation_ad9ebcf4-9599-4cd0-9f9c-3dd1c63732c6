package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_edit
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppGrid
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.domain.projects.details.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.details.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.domain.projects.details.models.SideEffect
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowPinkish
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource


class ProjectScreen(
    private val project: Project
) : BrowserScreen {

    override val urlSegment: String = "${project.id}"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                projects = TempDI.projects,
                termsGroups = TempDI.termsGroups,
                terms = TempDI.terms,
                beneficiaries = TempDI.beneficiaries,
                users = TempDI.users,
                coroutinesContext = emptyCoroutineContext
            )
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initDetailsState).value
        LaunchedEffect(Unit) { viewModel.initScreen(project) }
        LaunchedEffect(Unit) {
            viewModel.sideEffects.stream.collect { sideEffect ->
                when (sideEffect) {
                    is SideEffect.BackToProjectsScreen -> navigator?.browserPop()
                }
            }
        }

        AppBasePage(
            title = "الأعمال",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = true,
            isPullToRefreshEnabled = true,
            isPullToRefresh = !state.isBackEnabled,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = {
                navigator?.browserPop()
            }
        ) {
            val scope = rememberCoroutineScope()
            ProjectScreenContent(
                state = state,
                viewModel = viewModel
            )

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }

}

@Composable
private fun ProjectScreenContent(
    modifier: Modifier = Modifier,
    state: ScreenState,
    viewModel: ViewModel
) {
    val navigator = LocalNavigator.current

    AppLazyColumn(
        modifier = modifier.fillMaxSize(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
        alignment = Alignment.Start
    ) {

        item {
            var showEditSheet by remember { mutableStateOf(false) }

            AppSurface(
                modifier = Modifier.sectionsSpacing().shadowBluish(),
                padding = PaddingValues(Dimensions.Paddings.medium.dp),
            ) {
                AppColumn(
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                    alignment = Alignment.Start
                ) {
                    AppRow(
                        modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
                        arrangement = Arrangement.SpaceBetween,
                        alignment = Alignment.CenterVertically
                    ) {

                        Box(
                            modifier = Modifier.fillMaxHeight().weight(1f),
                            contentAlignment = Alignment.CenterStart
                        ) {
                            AppSubSectionTitleText(state.project?.name ?: "", color = MaterialTheme.colorScheme.primary)
                        }
                    }

                    AppLabeledBodyTextHorizontal(label = "الوصف", text = state.project?.description ?: "")

                    AppLabeledBodyTextHorizontal(label = "العميل", text = state.project?.customer?.name ?: "")

                    if (state.isEditProjectEnabled) {
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.CenterEnd
                        ) {
                            AppTextButton(
                                modifier = Modifier,
                                text = "تعديل",
                                enabled = state.isEditProjectEnabled,
                                iconPainter = painterResource(Res.drawable.ic_edit),
                                onClick = { showEditSheet = true }
                            )
                        }
                    }
                }
            }

            if (showEditSheet) {
                AddProjectSheet(
                    initialName = state.project?.name ?: "",
                    initialDescription = state.project?.description ?: "",
                    initialCustomer = state.project?.customer,
                    isEditing = true,
                    customers = state.customers,
                    onAddCustomer = { viewModel.refreshScreen() },
                    onDismissRequest = { showEditSheet = false },
                    isDeleteEnabled = state.isDeleteEnabled,
                    onDelete = { viewModel.deleteProject(state.project!!.id) },
                    onAdd = { name, description, customer ->
                        viewModel.editProject(name, description, customer.id, state.project!!.version)
                    }
                )
            }
        }

        item {
            AppGrid {
                if (state.isExpensesAccessEnabled) {
                    GridItem(
                        modifier = Modifier.adaptWidth().weight(1f),
                        top = "المصاريف",
                        bottom = state.projectExpensesText,
                        enabled = state.isExpensesEnabled,
                        onClick = { navigator?.browserPush(ProjectExpensesScreen(state.project!!)) }
                    )
                }

                if (state.isIncomesAccessEnabled) {
                    GridItem(
                        modifier = Modifier.adaptWidth().weight(1f),
                        top = "الإيرادات",
                        bottom = state.projectIncomesText,
                        enabled = state.isIncomesEnabled,
                        onClick = { navigator?.browserPush(ProjectIncomesScreen(state.project!!)) }
                    )
                }

                if (state.loggedInUser?.isAdmin == true) {
                    GridItem(
                        modifier = Modifier.adaptWidth().weight(1f),
                        top = "الربح",
                        bottom = state.projectEarningsText,
                        enabled = false,
                        onClick = { }
                    )

                    GridItem(
                        modifier = Modifier.adaptWidth().weight(1f),
                        top = "نسبة الربح",
                        bottom = (state.projectPercentageText) + " %",
                        enabled = false,
                        onClick = { }
                    )

                    GridItem(
                        modifier = Modifier.adaptWidth().weight(1f),
                        top = "متبقى إيرادات",
                        bottom = state.projectRemainingIncomesText,
                        enabled = false,
                        onClick = { }
                    )
                }
            }
        }

        if (state.loggedInUser?.isAdmin == true) {
            UsersAccessList(
                state = state,
                viewModel = viewModel
            )
        }
    }
}

@Composable
fun GridItem(
    modifier: Modifier = Modifier,
    top: String,
    bottom: String,
    enabled: Boolean,
    minHeight: Dp = Dimensions.HomeScreen.gridItemMinHeight,
    onClick: () -> Unit
) {
    AppCard(
        modifier = modifier.then(if (enabled) Modifier.shadowPinkish() else Modifier.shadowBluish()),
        onClick = onClick,
        enabled = enabled,
    ) {
        AppColumn(
            modifier = Modifier
                .fillMaxSize()
                .heightIn(min = minHeight)
                .padding(Dimensions.Paddings.small.dp),
            alignment = Alignment.CenterHorizontally,
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.CenterVertically)
        ) {
            AppTitleText(
                text = top,
                color = if (!enabled) MaterialTheme.colorScheme.tertiary else LocalContentColor.current
            )

            ForceLtr { AppTitleText(text = bottom, fontWeight = FontWeight.SemiBold) }
        }
    }
}