package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_add
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppFAB
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.AppGrid
import com.ryinex.accountant.client.shared.presentation.common.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.adaptWidth
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.customers.presentation.ui.CustomerSheet
import com.ryinex.accountant.client.shared.presentation.home.presentation.ui.NextArrowButton
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.data.customers.models.Customer
import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.domain.projects.home.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.home.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppDelayedConfirmButton
import com.ryinex.accountant.client.shared.presentation.common.dialogs.DelayConfirmDialog
import com.ryinex.accountant.client.shared.presentation.common.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource

class ProjectsScreen : BrowserScreen {

    override val urlSegment: String = "projects"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                TempDI.status,
                TempDI.projects,
                TempDI.customers,
                TempDI.users,
                emptyCoroutineContext
            )
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initListState).value
        var showAddSheet by remember { mutableStateOf(false) }

        LaunchedEffect(Unit) { viewModel.initScreen() }

        AppBasePage(
            title = "الأعمال",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = true,
            isPullToRefreshEnabled = true,
            isPullToRefresh = !state.isBackEnabled,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = { navigator?.browserPop() },
            fab = {
                if (state.loggedInUser?.isAdmin == true) {
                    AddFab(
                        isEnabled = state.isAddProjectEnabled,
                        onClick = { showAddSheet = true }
                    )
                }
            }
        ) {
            val scope = rememberCoroutineScope()

            ProjectsScreenContent(
                state = state,
                viewModel = viewModel
            )

            if (showAddSheet) {
                AddSheet(
                    state = state,
                    viewModel = viewModel,
                    onDismissRequest = { showAddSheet = false }
                )
            }

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }

    @Composable
    private fun ProjectsScreenContent(
        modifier: Modifier = Modifier,
        state: ScreenState,
        viewModel: ViewModel
    ) {
        AppLazyColumn(
            modifier = modifier.fillMaxSize(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.Start
        ) {
            if (state.loggedInUser?.isAdmin == true) {
                item {
                    var showAddSheet by remember { mutableStateOf(false) }

                    AppRow(
                        modifier = Modifier.fillMaxWidth().sectionsSpacing(),
                        arrangement = Arrangement.SpaceBetween,
                        alignment = Alignment.CenterVertically
                    ) {
                        AppSectionTitleText("")

                        AppTextButton(
                            modifier = Modifier,
                            text = "إضافة",
                            enabled = state.isAddProjectEnabled,
                            iconPainter = painterResource(Res.drawable.ic_add),
                            onClick = { showAddSheet = true }
                        )
                    }

                    if (showAddSheet) {
                        AddSheet(
                            state = state,
                            viewModel = viewModel,
                            onDismissRequest = { showAddSheet = false }
                        )
                    }
                }
            }

            ProjectsListSection(
                state = state,
                viewModel = viewModel
            )

            item {
                Spacer(modifier = Modifier.sectionsSpacing())
            }
        }

    }

    @Composable
    private fun AddSheet(
        state: ScreenState,
        viewModel: ViewModel,
        onDismissRequest: () -> Unit
    ) {
        AddProjectSheet(
            initialName = "",
            initialDescription = "",
            initialCustomer = null,
            isEditing = false,
            customers = state.customers,
            onAddCustomer = { viewModel.refreshScreen() },
            onDismissRequest = onDismissRequest,
            onAdd = { name, description, customer ->
                viewModel.addProject(name, description, isActive = true, customerId = customer.id)
            }
        )
    }

    @Composable
    private fun AddFab(
        isEnabled: Boolean,
        onClick: () -> Unit
    ) {
        AppFAB(
            enabled = isEnabled,
            onClick = onClick,
            icon = painterResource(Res.drawable.ic_add)
        )
    }
}


private fun LazyListScope.ProjectsListSection(
    state: ScreenState,
    viewModel: ViewModel
) {
    item {
        AppGrid {
            val navigator = LocalNavigator.current

            state.projects.forEach { project ->
                ProjectCard(
                    modifier = Modifier.adaptWidth(),
                    project = project,
                    state = state,
                )
            }
        }
    }
}

@Composable
private fun ProjectCard(
    modifier: Modifier = Modifier,
    state: ScreenState,
    project: Project
) {
    val navigator = LocalNavigator.current
    AppCard(
        modifier = modifier.adaptWidth().shadowBluish(),
        enabled = state.isProjectDetailsEnabled,
        onClick = { navigator?.browserPush(ProjectScreen(project)) },
        padding = Dimensions.Paddings.medium.dp,
        regularColors = true
    ) {
        AppRow(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.CenterVertically
        ) {
            AppColumn(
                modifier = Modifier.weight(1f),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
                alignment = Alignment.Start
            ) {
                AppSubSectionTitleText(text = project.name, color = MaterialTheme.colorScheme.primary)

                AppLabeledBodyTextHorizontal(label = "الوصف", text = project.description)

                if (state.loggedInUser?.isAdmin == true) {
                    AppLabeledBodyTextHorizontal(label = "المصاريف", text = project.totalExpenses.formatted())

                    AppLabeledBodyTextHorizontal(label = "الإيرادات", text = project.totalIncomes.formatted())
                }
            }

            NextArrowButton(
                enabled = state.isProjectDetailsEnabled,
                onClick = { navigator?.browserPush(ProjectScreen(project)) }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AddProjectSheet(
    initialName: String,
    initialDescription: String,
    initialCustomer: Customer?,
    isEditing: Boolean,
    customers: List<Customer>,
    isDeleteEnabled: Boolean = false,
    onAddCustomer: () -> Unit,
    onAdd: (String, String, Customer) -> Unit,
    onDelete: () -> Unit = {},
    onDismissRequest: () -> Unit
) {
    val minNameLength = remember { 3 }
    val minDescriptionLength = remember { 3 }

    AppBottomSheet(
        onDismissRequest = onDismissRequest
    ) {
        var name by remember { mutableStateOf(initialName) }
        var description by remember { mutableStateOf(initialDescription) }
        var customerText by remember { mutableStateOf(initialCustomer?.name ?: "") }
        var customer by remember { mutableStateOf(initialCustomer) }
        var showAddCustomer by remember { mutableStateOf(false) }
        var showDeleteDialog by remember { mutableStateOf(false) }

        val isNameError by remember(name) { mutableStateOf(name.trim().length < minNameLength) }
        val isDescriptionError by remember(description) { mutableStateOf(description.trim().length < minDescriptionLength) }

        val isEnabled by remember(isNameError, isDescriptionError, customer) {
            mutableStateOf(!isNameError && !isDescriptionError && customer != null)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = if (isEditing) initialName else "إضافة مشروع",
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = if (isEditing) "تعديل" else "إضافة",
                    enabled = isEnabled,
                    onClick = {
                        onAdd(name, description, customer!!)
                        onDismissRequest()
                    },
                )
            }

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = name,
                hint = "الاسم",
                enabled = true,
                isError = isNameError,
                onValueChange = { name = it },
                errorText = "المشروع يجب أن يكون على الأقل $minNameLength أحرف"
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = description,
                hint = "الوصف",
                enabled = true,
                isError = isDescriptionError,
                onValueChange = { description = it },
                errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength حروف"
            )

            AppSelectableAutoCompleteTextField(
                modifier = Modifier.fillMaxWidth(),
                text = customerText,
                hint = "العميل",
                enabled = true,
                isError = customer == null,
                minSuggestLength = 0,
                errorText = "يجب إختيار عميل للمشروع",
                allItems = customers,
                searchMapper = { it.name },
                displayMapper = { it.name },
                onItemClick = { customer = it },
                onEmptyItemClick = {
                    customer = null
                    showAddCustomer = true
                },
                items = customers,
                onValueChange = { customerText = it },
                selected = customer,
                onCancelSelected = { customer = null },
            )

            if (showAddCustomer) {
                CustomerSheet(
                    customer = null,
                    onDismissRequest = { showAddCustomer = false },
                    onFinish = {
                        customer = null
                        showAddCustomer = false
                        onAddCustomer()
                    }
                )
            }

            if (isEditing) {
                AppDelayedConfirmButton(
                    modifier = Modifier.align(Alignment.End),
                    text = "حذف المشروع",
                    onClick = { onDelete() },
                    enabled = isDeleteEnabled,
                    containerColor = MaterialTheme.colorScheme.error,
                    contentColor = MaterialTheme.colorScheme.onError
                )
            }
        }
    }
}