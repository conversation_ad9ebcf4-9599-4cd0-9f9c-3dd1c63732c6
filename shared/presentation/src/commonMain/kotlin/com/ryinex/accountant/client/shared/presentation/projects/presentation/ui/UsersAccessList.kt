package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_add
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.checkbox.AppCheckbox
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppGrid
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.feature.components.adaptWidth
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.domain.projects.details.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.details.models.ScreenState
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.data.users.models.UserProjectAccess
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import org.jetbrains.compose.resources.painterResource


fun LazyListScope.UsersAccessList(
    state: ScreenState,
    viewModel: ViewModel
) {
    item {
        var showAddSheet by remember { mutableStateOf(false) }

        AppRow(
            modifier = Modifier
                .fillMaxWidth()
                .sectionsSpacing(),
            arrangement = Arrangement.SpaceBetween,
            alignment = Alignment.CenterVertically
        ) {
            AppSectionTitleText("المستخدمين")

            AppTextButton(
                modifier = Modifier,
                text = "إضافة",
                enabled = state.isUsersAccessAddEnabled,
                iconPainter = painterResource(Res.drawable.ic_add),
                onClick = {
                    showAddSheet = true
                }
            )
        }


        if (showAddSheet) {
            AddUserAccessSheet(
                state = state,
                viewModel = viewModel,
                onDismissRequest = {
                    showAddSheet = false
                }
            )
        }
    }

    item {
        AppGrid {
            state.usersAccess.forEach { user ->
                UserCard(
                    userProjectAccess = user,
                    viewModel = viewModel
                )
            }
        }
    }
}

@Composable
internal fun UserCard(
    modifier: Modifier = Modifier,
    userProjectAccess: UserProjectAccess,
    viewModel: ViewModel
) {
    AppCard(
        modifier = modifier.adaptWidth().shadowBluish()
    ) {
        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            var showModifyDialog by remember { mutableStateOf(false) }

            AppSubSectionTitleText(
                text = userProjectAccess.user.name,
                color = MaterialTheme.colorScheme.primary
            )

            AppBodyText(
                modifier = Modifier.padding(bottom = Dimensions.Paddings.small.dp),
                text = userProjectAccess.user.phoneNumber,
            )

            HorizontalDivider()

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                AppLabelText(text = "الصلاحيات")

                AppTextButton(
                    modifier = Modifier,
                    text = "تعديل",
                    enabled = true,
                    onClick = { showModifyDialog = true }
                )
            }

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {
                AppCheckbox(
                    checked = userProjectAccess.projectAccess.expensesAccess,
                    onCheckedChange = {},
                    enabled = false
                )

                AppBodyText(text = "المصاريف")
            }

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {
                AppCheckbox(
                    checked = userProjectAccess.projectAccess.incomesAccess,
                    onCheckedChange = {},
                    enabled = false
                )

                AppBodyText(text = "الإيرادات")
            }

            HorizontalDivider()

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إزالة",
                    enabled = true,
                    onClick = { viewModel.removeUserAccess(userId = userProjectAccess.user.id) }
                )
            }

            if (showModifyDialog) {
                ModifyUserAccessSheet(
                    userProjectAccess = userProjectAccess,
                    onDismissRequest = {
                        showModifyDialog = false
                    },
                    onModifyUserAccess = { expensesAccess, incomesAccess ->
                        viewModel.modifyUserAccess(
                            userId = userProjectAccess.user.id,
                            expensesAccess = expensesAccess,
                            incomesAccess = incomesAccess,
                            version = userProjectAccess.projectAccess.version
                        )
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddUserAccessSheet(
    state: ScreenState,
    viewModel: ViewModel,
    onDismissRequest: () -> Unit
) {
    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var expensesAccess by remember { mutableStateOf(false) }
        var incomesAccess by remember { mutableStateOf(false) }
        var nameText by remember { mutableStateOf("") }
        var user by remember { mutableStateOf<User?>(null) }

        val isEnabled by remember(user, expensesAccess, incomesAccess) {
            mutableStateOf(user != null && (expensesAccess || incomesAccess))
        }

        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            item {
                AppSectionTitleText("إضافة مستخدم")
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppTextButton(
                        modifier = Modifier,
                        text = "إلغاء",
                        enabled = true,
                        onClick = { onDismissRequest() }
                    )

                    AppTextButton(
                        modifier = Modifier,
                        text = "إضافة",
                        enabled = isEnabled,
                        onClick = {
                            viewModel.addUserAccess(
                                userId = user!!.id,
                                expensesAccess = expensesAccess,
                                incomesAccess = incomesAccess
                            )
                            onDismissRequest()
                        },
                    )
                }
            }

            item {
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = nameText,
                    hint = "المستخدم",
                    enabled = true,
                    isError = false,
                    minSuggestLength = 0,
                    errorText = "",
                    allItems = state.nonAccessUsers,
                    searchMapper = { it.name },
                    displayMapper = { it.name },
                    onItemClick = { user = it },
                    onEmptyItemClick = { user = null },
                    items = state.nonAccessUsers,
                    onValueChange = { nameText = it },
                    selected = user,
                    onCancelSelected = { user = null }
                )
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                    alignment = Alignment.CenterVertically
                ) {
                    AppCheckbox(
                        modifier = Modifier,
                        checked = expensesAccess,
                        onCheckedChange = { expensesAccess = it },
                        enabled = true
                    )

                    AppBodyText(text = "المصاريف")
                }
            }
            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                    alignment = Alignment.CenterVertically
                ) {
                    AppCheckbox(
                        modifier = Modifier,
                        checked = incomesAccess,
                        onCheckedChange = { incomesAccess = it },
                        enabled = true
                    )

                    AppBodyText(text = "الإيرادات")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ModifyUserAccessSheet(
    userProjectAccess: UserProjectAccess,
    onModifyUserAccess: (Boolean, Boolean) -> Unit,
    onDismissRequest: () -> Unit
) {
    AppBottomSheet(
        onDismissRequest = onDismissRequest
    ) {
        var expensesAccess by remember { mutableStateOf(userProjectAccess.projectAccess.expensesAccess) }
        var incomesAccess by remember { mutableStateOf(userProjectAccess.projectAccess.incomesAccess) }

        val isEnabled by remember(expensesAccess, incomesAccess) {
            mutableStateOf(expensesAccess || incomesAccess)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = "تعديل الصلاحيات"
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "تعديل",
                    enabled = isEnabled,
                    onClick = {
                        onModifyUserAccess(expensesAccess, incomesAccess)
                        onDismissRequest()
                    },
                )
            }

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {
                AppCheckbox(
                    modifier = Modifier,
                    checked = expensesAccess,
                    onCheckedChange = { expensesAccess = it },
                    enabled = true
                )

                AppBodyText(text = "المصاريف")
            }

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {
                AppCheckbox(
                    modifier = Modifier,
                    checked = incomesAccess,
                    onCheckedChange = { incomesAccess = it },
                    enabled = true
                )

                AppBodyText(text = "الإيرادات")
            }
        }
    }

}