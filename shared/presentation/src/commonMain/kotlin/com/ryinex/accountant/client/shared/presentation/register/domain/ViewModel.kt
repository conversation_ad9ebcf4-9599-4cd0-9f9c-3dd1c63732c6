package com.ryinex.accountant.client.shared.presentation.register.domain

import cafe.adriel.voyager.core.model.ScreenModel
import cafe.adriel.voyager.core.model.screenModelScope
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequest
import com.ryinex.accountant.client.shared.data.auth.repositories.AuthRepository
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI.users
import com.ryinex.accountant.client.shared.data.common.settings.UserSettings
import com.ryinex.accountant.client.shared.data.organization.repositories.OrganizationsRepository
import com.ryinex.accountant.client.shared.domain.welcome.login.handle
import com.ryinex.accountant.client.shared.presentation.common.domain.repositories.SideEffectsRepository
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async

class ViewModel(
    private val status: StatusRepository,
    private val auth: AuthRepository,
    private val organization: OrganizationsRepository,
    private val logger: Logger
) : ScreenModel {
    private val scope = screenModelScope
    val sideEffects = SideEffectsRepository<SideEffect>()
    fun register(request: RegisterRequest) = scope.handle {
        val result = status.execute(
            loading = Message.fromString("جاري التسجيل ..."),
            success = Message.fromString("تم التسجيل"),
            job = { registerJob(request) }
        )

        if (result is AsyncResult.Fail) throw result.error
    }

    private suspend fun registerJob(request: RegisterRequest) = logger.async {
        val response = auth.register(request)
        val auth = auth.login(
            username = response.user.username,
            password = request.user.password,
            organizationCode = response.organization.organizationCode
        )
        val user = users.getUserById(auth.userId)
        val organization = organization.getOrganization()
        UserSettings.organization.set(organization)
        sideEffects.emit(SideEffect.LoginSuccess(user, organization))
    }
}