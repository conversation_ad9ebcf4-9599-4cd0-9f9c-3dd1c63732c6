package com.ryinex.accountant.client.shared.presentation.register.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.logo_word
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.presentation.common.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.AppRow
import com.ryinex.accountant.client.shared.presentation.common.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppSecondaryButton
import com.ryinex.accountant.client.shared.presentation.common.dialogs.DelayConfirmDialog
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequest
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequestOrganization
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequestUser
import com.ryinex.accountant.client.shared.presentation.register.domain.ViewModel
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.theme.TajawalFontFamily
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import kotlin.time.Duration.Companion.seconds

// Extension function to convert ScreenState to RegisterRequest
internal fun ScreenState.toRequest(): RegisterRequest {
    return RegisterRequest(
        organization = RegisterRequestOrganization(
            name = ui.organization.name.text.get(),
            phoneNumber = ui.organization.phoneNumber.text.get(),
            description = ui.organization.description.text.get()
        ),
        user = RegisterRequestUser(
            name = ui.user.name.text.get(),
            username = ui.user.username.text.get(),
            phoneNumber = ui.user.phoneNumber.text.get(),
            password = ui.user.password.text.get(),
            confirmPassword = ui.user.confirmPassword.text.get(),
            isAdmin = true
        )
    )
}

@Composable
internal fun PagerScreen(
    count: Int,
    current: Int,
    bottom: @Composable ColumnScope.() -> Unit,
    content: @Composable BoxScope.() -> Unit
) {
    AppColumn(
        modifier = Modifier.fillMaxSize(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
        alignment = Alignment.CenterHorizontally
    ) {
        Box(modifier = Modifier.weight(1f)) { content() }

        AppColumn(
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.CenterHorizontally
        ) {
            Dots(count = count, selectedIndex = current)
            bottom()
        }
    }
}

@Composable
internal fun WelcomePage(
    modifier: Modifier = Modifier
) {
    AppColumn(
        modifier = modifier.fillMaxHeight(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.large.dp, Alignment.CenterVertically),
        alignment = if (LocalIsDesktop.current) Alignment.CenterHorizontally else Alignment.CenterHorizontally
    ) {
        val primaryColor = MaterialTheme.colorScheme.primary
        val tertiaryColor = MaterialTheme.colorScheme.tertiaryContainer

        val text = remember {
            buildAnnotatedString {
                withStyle(SpanStyle(fontWeight = FontWeight.Medium, color = primaryColor)) { append("تسجيل\n") }
                withStyle(SpanStyle(fontWeight = FontWeight.Bold, color = tertiaryColor)) { append("مؤسسة ") }
                withStyle(SpanStyle(fontWeight = FontWeight.Medium)) { append("جديدة\n") }
                withStyle(SpanStyle(fontWeight = FontWeight.Light)) { append("بخطوات ") }
                withStyle(SpanStyle(fontWeight = FontWeight.Bold, color = tertiaryColor)) { append("بسيطة") }
            }
        }

        Image(
            modifier = Modifier.width(256.dp),
            painter = painterResource(Res.drawable.logo_word),
            contentDescription = "Settings",
        )

        Text(modifier = Modifier.fillMaxWidth(), text = text, lineHeight = 48.sp, fontSize = 48.sp, fontFamily = TajawalFontFamily)

        AppBodyText(modifier = Modifier.fillMaxWidth(), text = "ابدأ الآن بتسجيل مؤسستك واستمتع بتجربة استخدام تطبيق ذكي يُسهّل إدارة حساباتك، ويجمعك مع شركائك وموظفيك في منصة واحدة لتنظيم الأعمال والمشاريع بكل احترافية، مع إمكانية مشاركة الحسابات بسلاسة مع عملائك والمستفيدين من تجار و وكلاء و مقاوليين.")
    }
}

@Composable
internal fun NextButton(
    modifier: Modifier = Modifier,
    pager: PagerState,
    enabled: Boolean = true
) {
    val scope = rememberCoroutineScope()
    AppButton(
        modifier = modifier,
        text = "التالي",
        enabled = enabled
    ) { scope.launch { pager.animateScrollToPage(pager.currentPage + 1) } }
}

@Composable
internal fun PreviousButton(
    modifier: Modifier = Modifier,
    pager: PagerState,
    enabled: Boolean = true
) {
    val scope = rememberCoroutineScope()
    AppSecondaryButton(
        modifier = modifier,
        text = "السابق",
        enabled = enabled
    ) { scope.launch { pager.animateScrollToPage(pager.currentPage - 1) } }
}

@Composable
internal fun RegisterButton(
    modifier: Modifier = Modifier,
    viewModel: ViewModel,
    state: ScreenState,
    enabled: Boolean = true
) {
    AppButton(
        modifier = modifier,
        text = "تسجيل",
        enabled = enabled
    ) { viewModel.register(state.toRequest()) }
}

@Composable
internal fun Dots(modifier: Modifier = Modifier, count: Int, selectedIndex: Int) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.CenterVertically
    ) {
        repeat(count) { current ->
            val color = MaterialTheme.colorScheme.tertiary
            Box(
                modifier = Modifier
                    .padding(2.dp)
                    .clip(CircleShape)
                    .background(if (current == selectedIndex) color else MaterialTheme.colorScheme.onTertiary)
                    .border(1.dp, color, CircleShape)
                    .size(8.dp)
            )
        }
    }
}

@Composable
internal fun ConfirmRegisterDialog(user: User, organization: Organization, onConfirm: () -> Unit) {
    val title = remember { "مبروووك " }

    DelayConfirmDialog(
        title = title,
        body = { ConfirmRegisterDialogContent(user, organization) },
        onConfirm = { onConfirm() },
        onDismiss = null,
        confirmContainerColor = MaterialTheme.colorScheme.primary,
        confirmTextColor = MaterialTheme.colorScheme.onPrimary,
        delay = 5.seconds
    )
}

@Composable
internal fun OrganizationForm(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.large.dp),
        alignment = if (LocalIsDesktop.current) Alignment.CenterHorizontally else Alignment.Start
    ) {
        AppSubSectionTitleText("بيانات المؤسسة")

        OrganizationNameField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )

        OrganizationPhoneField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )

        OrganizationDescriptionField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )
    }
}

@Composable
internal fun UserForm(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.large.dp),
        alignment = if (LocalIsDesktop.current) Alignment.CenterHorizontally else Alignment.Start
    ) {
        AppSubSectionTitleText("بيانات المستخدم")

        UserNameField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )

        UserUsernameField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )

        UserPhoneField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )

        UserPasswordField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )

        UserConfirmPasswordField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state
        )
    }
}

// Organization Form Fields
@Composable
internal fun OrganizationNameField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.organization.name,
        hint = "اسم المؤسسة",
        helperText = "إسم المؤسسة هو الإسم الذي سيظهر في التطبيق بالكامل، " +
                "مثال: \"مكتب البناء للمقاولات\"، \"شركة الفتح للتوريدات\"، \"محمود لمقاولات الحفر\"",
        errorText = "الاسم يجب أن يكون على الأقل ${state.ui.organization.nameMinLength} أحرف",
        enabled = true
    )
}

@Composable
internal fun OrganizationPhoneField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.organization.phoneNumber,
        hint = "رقم الهاتف",
        helperText = "قد يتم إستخدام رقم الهاتف لتفعيل مؤسستك او استرجاع البيانات إذا تم فقدانها",
        errorText = "الرجاء إدخال رقم هاتف صحيح",
        enabled = true
    )
}

@Composable
internal fun OrganizationDescriptionField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.organization.description,
        hint = "وصف المؤسسة",
        helperText = "وصف مختصر عن المؤسسة او الأعمال التي تقوم بها",
        errorText = "الوصف يجب أن يكون على الأقل ${state.ui.organization.descriptionMinLength} أحرف",
        enabled = true
    )
}

// User Form Fields
@Composable
internal fun UserNameField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.user.name,
        hint = "الاسم",
        helperText = "إسمك ثنائي، مثال: \"محمود رزق\"",
        errorText = "الاسم يجب أن يكون على الأقل ${state.ui.user.nameMinLength} أحرف",
        enabled = true
    )
}

@Composable
internal fun UserUsernameField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.user.username,
        hint = "المستخدم",
        helperText = "إسم المستخدم الذي ستقوم بتسجيل الدخول به الرجاء الاحتفاظ به و عدم فقدانه، مثال: \"mahmoud.rizk\"",
        errorText = "إسم المستخدم يجب أن يكون على الأقل ${state.ui.user.usernameMinLength} أحرف باللغة الإنجليزية لا تحتوى على مسافات",
        enabled = true
    )
}

@Composable
internal fun UserPhoneField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.user.phoneNumber,
        hint = "رقم الهاتف",
        helperText = "قد يتم إستخدام رقم الهاتف لتفعيل مؤسستك او استرجاع البيانات إذا تم فقدانها",
        errorText = "الرجاء إدخال رقم هاتف صحيح",
        enabled = true
    )
}

@Composable
internal fun UserPasswordField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.user.password,
        hint = "كلمة المرور",
        helperText = "كلمة المرور المستخدمة لتسجيل الدخول",
        errorText = "كلمة المرور يجب أن تكون على الأقل ${state.ui.user.passwordMinLength} أحرف باللغة الإنجليزية لا تحتوى على مسافات",
        enabled = true
    )
}

@Composable
internal fun UserConfirmPasswordField(
    modifier: Modifier = Modifier,
    state: ScreenState
) {
    AppTextField(
        modifier = modifier,
        state = state.ui.user.confirmPassword,
        hint = "تأكيد كلمة المرور",
        helperText = "تأكيد كلمة المرور",
        errorText = "كلمة المرور غير متطابقة",
        enabled = true
    )
}

@Composable
private fun ConfirmRegisterDialogContent(user: User, organization: Organization) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.Start
    ) {
        AppBodyText(text = "لقد تم التسجيل بنجاح")
        AppLabeledBodyTextHorizontal(label = "اسم المستخدم", text = user.username)
        AppLabeledBodyTextHorizontal(label = "كود المؤسسة", text = organization.organizationCode)
        AppBodyText(text = "الرجاء أخذ لقطة للشاشة (Screenshot) او تسجيلها والأحتفاظ بهذه المعلومات لعدم فقدان الوصول لمؤسستك")
    }
}
