package com.ryinex.accountant.client.shared.presentation.register.presentation

import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequest
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequestOrganization
import com.ryinex.accountant.client.shared.data.auth.models.RegisterRequestUser

/**
 * Extension function to convert ScreenState to RegisterRequest
 * This mapper extracts all form data from the UI state and creates the appropriate request object
 */
fun ScreenState.toRequest(): RegisterRequest {
    return RegisterRequest(
        organization = ui.organization.toRequestOrganization(),
        user = ui.user.toRequestUser()
    )
}

/**
 * Extension function to convert OrganizationFormState to RegisterRequestOrganization
 */
private fun OrganizationFormState.toRequestOrganization(): RegisterRequestOrganization {
    return RegisterRequestOrganization(
        name = name.text.get().trim(),
        phoneNumber = phoneNumber.text.get().trim(),
        description = description.text.get().trim()
    )
}

/**
 * Extension function to convert UserFormState to RegisterRequestUser
 */
private fun UserFormState.toRequestUser(): RegisterRequestUser {
    return RegisterRequestUser(
        name = name.text.get().trim(),
        username = username.text.get().trim(),
        phoneNumber = phoneNumber.text.get().trim(),
        password = password.text.get(),
        confirmPassword = confirmPassword.text.get(),
        isAdmin = true // Default to admin for organization creator
    )
}
