package com.ryinex.accountant.client.shared.presentation.register.presentation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.presentation.register.domain.SideEffect
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.login.presentation.LoginScreen
import com.ryinex.accountant.client.shared.presentation.register.domain.ViewModel
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import kotlinx.coroutines.launch

class RegisterScreen : BrowserScreen {

    override val urlSegment: String = "signup"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                auth = TempDI.auth,
                organization = TempDI.organizations,
                logger = TempDI.logger
            )
        }
        val screenState = remember { ScreenState(TempDI.status) }
        val pages = remember(screenState.ui.organization.isOrganizationFormValid.listen) {
            if (screenState.ui.organization.isOrganizationFormValid.get()) 3 else 2
        }
        val pager = rememberPagerState { pages }

        var showConfirmDialog by remember { mutableStateOf(false) }
        var user by remember { mutableStateOf<User?>(null) }
        var organization by remember { mutableStateOf<Organization?>(null) }

        LaunchedEffect(Unit) {
            viewModel.sideEffects.stream.collect {
                when (it) {
                    is SideEffect.LoginSuccess -> {
                        user = it.user
                        organization = it.organization
                        showConfirmDialog = true
                    }
                }
            }
        }

        AppBasePage(
            title = "مؤسسة جديدة",
            isBackEnabled = pager.currentPage == 0,
            isPullToRefreshEnabled = false,
            isPullToRefresh = false,
            onBack = { navigator?.browserPop() },
            onPullToRefresh = { },
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp)
        ) {
            val scope = rememberCoroutineScope()

            ScreenPager(screenState = screenState, pager = pager, viewModel = viewModel, pages = pages)

            AppStatusBar(
                statuses = screenState.ui.status.listen,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }

        if (showConfirmDialog) {
            ConfirmRegisterDialog(
                user = user!!,
                organization = organization!!,
                onConfirm = {
                    showConfirmDialog = false
                    navigator?.browserPush(LoginScreen(user, organization))
                }
            )
        }
    }
}

@Composable
private fun ScreenPager(
    viewModel: ViewModel,
    screenState: ScreenState,
    pager: PagerState,
    pages: Int
) {

    val scope = rememberCoroutineScope()

    HorizontalPager(
        state = pager,
        pageSpacing = Dimensions.Paddings.medium.dp,
        contentPadding = PaddingValues(bottom = Dimensions.Paddings.medium.dp)
    ) {
        when (it) {
            0 -> WelcomeScreen(pages, it, pager)

            1 -> OrganizationScreen(pages, it, pager, screenState)

            2 -> UserScreen(pages, it, pager, viewModel, screenState)
        }
    }
}

@Composable
private fun UserScreen(pages: Int, it: Int, pager: PagerState, viewModel: ViewModel, screenState: ScreenState) {
    PagerScreen(
        count = pages,
        current = it,
        bottom = {
            AppRow(
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                alignment = Alignment.CenterVertically
            ) {
                PreviousButton(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    pager = pager,
                    enabled = true
                )
                RegisterButton(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    viewModel = viewModel,
                    state = screenState,
                    enabled = screenState.ui.user.isUserFormValid.listen
                )
            }
        },
        content = {
            UserForm(
                modifier = Modifier.fillMaxHeight().verticalScroll(rememberScrollState()),
                state = screenState
            )
        }
    )
}

@Composable
private fun OrganizationScreen(
    pages: Int,
    it: Int,
    pager: PagerState,
    screenState: ScreenState
) {
    PagerScreen(
        count = pages,
        current = it,
        bottom = {
            AppRow(
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                alignment = Alignment.CenterVertically
            ) {
                PreviousButton(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    pager = pager,
                    enabled = true
                )
                NextButton(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    pager = pager,
                    enabled = screenState.ui.organization.isOrganizationFormValid.listen
                )
            }
        },
        content = {
            OrganizationForm(
                modifier = Modifier.fillMaxHeight().verticalScroll(rememberScrollState()),
                state = screenState
            )
        }
    )
}

@Composable
private fun WelcomeScreen(pages: Int, it: Int, pager: PagerState) {
    PagerScreen(
        count = pages,
        current = it,
        bottom = { NextButton(modifier = Modifier.fillMaxWidth(), pager = pager, enabled = true) },
        content = { WelcomePage() }
    )
}








