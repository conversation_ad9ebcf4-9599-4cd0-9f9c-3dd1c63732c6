package com.ryinex.accountant.client.shared.presentation.register.presentation

import com.ryinex.accountant.client.shared.presentation.common.AppTextFieldState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.texts.validPassword
import com.ryinex.accountant.client.shared.presentation.common.texts.validPhoneNumber
import com.ryinex.accountant.client.shared.presentation.common.texts.validUsername
import core.common.status.Status
import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge

class ScreenState(
    status: StatusRepository,
    val ui: UIState = UIState(status = status)
)

class UIState(
    status: StatusRepository
) {
    val organization = OrganizationFormState()
    val user = UserFormState()
    val status = DerivedState(status.stream.value, status.stream.map { it.filter { it !is Status.Success } })
}

class OrganizationFormState {
    val nameMinLength = 3
    val name = AppTextFieldState { it.map { it.trim().length < nameMinLength } }
    
    val phoneNumber = AppTextFieldState { it.map { !it.validPhoneNumber() } }
    
    val descriptionMinLength = 3
    val description = AppTextFieldState { it.map { it.trim().length < descriptionMinLength } }
    
    private val organizationFormValidFlow = combine(
        name.error.stream,
        phoneNumber.error.stream,
        description.error.stream
    ) { nameError, phoneError, descriptionError ->
        !nameError && !phoneError && !descriptionError
    }
    val isOrganizationFormValid = DerivedState(false, organizationFormValidFlow)
}

class UserFormState {
    val nameMinLength = 3
    val name = AppTextFieldState { it.map { it.trim().length < nameMinLength } }
    
    val usernameMinLength = 3
    val username = AppTextFieldState { it.map { it.trim().length < usernameMinLength || !it.validUsername() } }
    
    val phoneNumber = AppTextFieldState { it.map { !it.validPhoneNumber() } }
    
    val passwordMinLength = 6
    val password = AppTextFieldState { it.map { it.trim().length < passwordMinLength || !it.validPassword() } }
    
    val confirmPassword = AppTextFieldState { it.map { 
        !it.validPassword() || it != password.text.get() 
    } }
    
    private val userFormValidFlow = combine(
        name.error.stream,
        username.error.stream,
        phoneNumber.error.stream,
        password.error.stream,
        confirmPassword.error.stream
    ) { nameError, usernameError, phoneError, passwordError, confirmPasswordError ->
        !nameError && !usernameError && !phoneError && !passwordError && !confirmPasswordError
    }
    val isUserFormValid = DerivedState(false, userFormValidFlow)
}
