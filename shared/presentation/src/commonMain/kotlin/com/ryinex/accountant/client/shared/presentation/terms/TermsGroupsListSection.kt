package com.ryinex.accountant.client.shared.presentation.terms

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_edit
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.feature.components.adaptWidth
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.domain.terms.ViewModel
import com.ryinex.accountant.client.shared.domain.terms.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import org.jetbrains.compose.resources.painterResource


internal fun LazyStaggeredGridScope.TermsGroupsListSection(
    viewModel: ViewModel,
    state: ScreenState
) {

    state.terms.forEach { termsGroup ->
        item {
            TermsGroupCard(
                map = termsGroup,
                isEnabled = state.isGroupDetailsEnabled,
                groups = state.groups,
                onFinishAddEdit = { viewModel.refreshScreen() }
            )
        }
    }
}

@Composable
private fun TermsGroupCard(
    modifier: Modifier = Modifier,
    map: Map.Entry<TermsGroup, List<Term>>,
    groups: List<TermsGroup>,
    isEnabled: Boolean,
    onFinishAddEdit: () -> Unit
) {
    val navigator = LocalNavigator.current
    AppCard(
        modifier = modifier.adaptWidth().shadowBluish(),
    ) {
        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            var showEditSheet by remember { mutableStateOf(false) }
            var showAddSheet by remember { mutableStateOf(false) }

            AppColumn(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.Top,
                alignment = Alignment.Start
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {
                    AppSubSectionTitleText(text = map.key.name, color = MaterialTheme.colorScheme.primary)

                    AppColumn(
                        arrangement = Arrangement.Top,
                        alignment = Alignment.Start
                    ) {
                        AppTextButton(
                            enabled = isEnabled,
                            onClick = { showEditSheet = true },
                            text = "تعديل"
                        )
                    }
                }

                if (map.key.description != map.key.name) {
                    AppBodyText(text = map.key.description, color = MaterialTheme.colorScheme.onBackground)
                }
            }

            HorizontalDivider()

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                AppLabelText("البنود")

                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    AppTextButton(
                        enabled = isEnabled,
                        onClick = { showAddSheet = true },
                        text = "إضافة"
                    )
                }
            }

            for (term in map.value) {
                TermRow(
                    term = term,
                    isEnabled = isEnabled,
                    onEdit = onFinishAddEdit
                )
            }


            if (showEditSheet) {
                TermsGroupSheet(
                    termsGroup = map.key,
                    onDismissRequest = { showEditSheet = false },
                    onFinish = onFinishAddEdit
                )
            }

            if (showAddSheet) {
                TermSheet(
                    isCancelTermsGroupEnabled = false,
                    onDismissRequest = { showAddSheet = false },
                    onFinish = onFinishAddEdit
                )
            }
        }
    }
}

@Composable
private fun TermRow(
    modifier: Modifier = Modifier,
    term: Term,
    isEnabled: Boolean,
    onEdit: () -> Unit
) {
    var showEditSheet by remember { mutableStateOf(false) }

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.SpaceBetween,
        alignment = Alignment.Bottom
    ) {
        AppColumn(
            modifier = Modifier.weight(1f),
            arrangement = Arrangement.Top,
            alignment = Alignment.Start
        ) {
            AppTitleText(
                text = term.name,
                color = MaterialTheme.colorScheme.onBackground
            )

            if (term.description != term.name) {
                AppBodyText(text = term.description)
            }
        }

        AppSupportImageViewButton(
            painter = painterResource(Res.drawable.ic_edit),
            iconOnly = true,
            enabled = isEnabled,
            onClick = { showEditSheet = true }
        )
    }

    if (showEditSheet) {
        TermSheet(
            term = term,
            onDismissRequest = { showEditSheet = false },
            onFinish = onEdit
        )
    }
}