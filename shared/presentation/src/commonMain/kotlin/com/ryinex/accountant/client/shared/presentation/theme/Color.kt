package com.ryinex.accountant.client.shared.presentation.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColor

private val primaryLight = Color(0xFF00316F)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFF1F53A3)
private val onPrimaryContainerLight = Color(0xFFFFFFFF)
private val secondaryLight = Color(0xFF344362)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFF657496)
private val onSecondaryContainerLight = Color(0xFFFFFFFF)
private val tertiaryLight = Color(0xFF69002E)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFA2194F)
private val onTertiaryContainerLight = Color(0xFFFFFFFF)
private val errorLight = Color(0xFF8C0009)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFDA342E)
private val onErrorContainerLight = Color(0xFFFFFFFF)
private val backgroundLight = Color(0xFFFFFFFF)
private val onBackgroundLight = Color(0xFF1A1B21)
private val surfaceLight = Color(0xFFF3F5FA)
private val onSurfaceLight = Color(0xFF1A1B21)
private val surfaceVariantLight = Color(0xFFDFE2EF)
private val onSurfaceVariantLight = Color(0xFF3F434D)
private val outlineLight = Color(0xFF5B5F6A)
private val outlineVariantLight = Color(0xFF777A86)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF2E3036)
private val inverseOnSurfaceLight = Color(0xFFF0F0F8)
private val inversePrimaryLight = Color(0xFFADC6FF)
private val surfaceDimLight = Color(0xFFD9D9E1)
private val surfaceBrightLight = Color(0xFFF9F9FF)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFF3F3FA)
private val surfaceContainerLight = Color(0xFFEDEDF5)
private val surfaceContainerHighLight = Color(0xFFE8E7EF)
private val surfaceContainerHighestLight = Color(0xFFE2E2E9)

private val primaryDark= Color(0xFFB4CBFF)
private val onPrimaryDark= Color(0xFF001537)
private val primaryContainerDark= Color(0xFF6490E4)
private val onPrimaryContainerDark= Color(0xFF000000)
private val secondaryDark= Color(0xFFBBCBF1)
private val onSecondaryDark= Color(0xFF041533)
private val secondaryContainerDark= Color(0xFF8191B4)
private val onSecondaryContainerDark= Color(0xFF000000)
private val tertiaryDark= Color(0xFFFFB7C7)
private val onTertiaryDark= Color(0xFF350014)
private val tertiaryContainerDark= Color(0xFFF25A8A)
private val onTertiaryContainerDark= Color(0xFF111318)
private val errorDark= Color(0xFFFFBAB1)
private val onErrorDark= Color(0xFF370001)
private val errorContainerDark= Color(0xFFFF5449)
private val onErrorContainerDark= Color(0xFF000000)
private val backgroundDark= Color(0xFF111318)
private val onBackgroundDark= Color(0xFFE2E2E9)
private val surfaceDark= Color(0xFF111318)
private val onSurfaceDark= Color(0xFFFBFAFF)
private val surfaceVariantDark= Color(0xFF434751)
private val onSurfaceVariantDark= Color(0xFFC7CAD7)
private val outlineDark= Color(0xFF9FA2AF)
private val outlineVariantDark= Color(0xFF7F838F)
private val scrimDark= Color(0xFF000000)
private val inverseSurfaceDark= Color(0xFFE2E2E9)
private val inverseOnSurfaceDark= Color(0xFF282A2F)
private val inversePrimaryDark= Color(0xFF054595)
private val surfaceDimDark= Color(0xFF111318)
private val surfaceBrightDark= Color(0xFF37393F)
private val surfaceContainerLowestDark= Color(0xFF0C0E13)
private val surfaceContainerLowDark= Color(0xFF1A1B21)
private val surfaceContainerDark= Color(0xFF1E1F25)
private val surfaceContainerHighDark= Color(0xFF282A2F)
private val surfaceContainerHighestDark= Color(0xFF33353A)

val primary = AppColor(primaryLight, primaryDark)
val onPrimary = AppColor(onPrimaryLight, onPrimaryDark)
val primaryContainer = AppColor(primaryContainerLight, primaryContainerDark)
val onPrimaryContainer = AppColor(onPrimaryContainerLight, onPrimaryContainerDark)
val secondary = AppColor(secondaryLight, secondaryDark)
val onSecondary = AppColor(onSecondaryLight, onSecondaryDark)
val secondaryContainer = AppColor(secondaryContainerLight, secondaryContainerDark)
val onSecondaryContainer = AppColor(onSecondaryContainerLight, onSecondaryContainerDark)
val tertiary = AppColor(tertiaryLight, tertiaryDark)
val onTertiary = AppColor(onTertiaryLight, onTertiaryDark)
val tertiaryContainer = AppColor(tertiaryContainerLight, tertiaryContainerDark)
val onTertiaryContainer = AppColor(onTertiaryContainerLight, onTertiaryContainerDark)
val error = AppColor(errorLight, errorDark)
val onError = AppColor(onErrorLight, onErrorDark)
val errorContainer = AppColor(errorContainerLight, errorContainerDark)
val onErrorContainer = AppColor(onErrorContainerLight, onErrorContainerDark)
val background = AppColor(backgroundLight, backgroundDark)
val onBackground = AppColor(onBackgroundLight, onBackgroundDark)
val surface = AppColor(surfaceLight, surfaceDark)
val onSurface = AppColor(onSurfaceLight, onSurfaceDark)
val surfaceVariant = AppColor(surfaceVariantLight, surfaceVariantDark)
val onSurfaceVariant = AppColor(onSurfaceVariantLight, onSurfaceVariantDark)
val outline = AppColor(outlineLight, outlineDark)
val outlineVariant = AppColor(outlineVariantLight, outlineVariantDark)
val scrim = AppColor(scrimLight, scrimDark)

val LightColorScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val DarkColorScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)

internal val LightColors = LightColorScheme
internal val DarkColors = DarkColorScheme

@Composable
fun Color.on(): Color {
    return when (this) {
        MaterialTheme.colorScheme.primary -> MaterialTheme.colorScheme.onPrimary
        MaterialTheme.colorScheme.onPrimary -> MaterialTheme.colorScheme.primary
        MaterialTheme.colorScheme.primaryContainer -> MaterialTheme.colorScheme.onPrimaryContainer
        MaterialTheme.colorScheme.onPrimaryContainer -> MaterialTheme.colorScheme.primaryContainer
        MaterialTheme.colorScheme.secondary -> MaterialTheme.colorScheme.onSecondary
        MaterialTheme.colorScheme.onSecondary -> MaterialTheme.colorScheme.secondary
        MaterialTheme.colorScheme.secondaryContainer -> MaterialTheme.colorScheme.onSecondaryContainer
        MaterialTheme.colorScheme.onSecondaryContainer -> MaterialTheme.colorScheme.secondaryContainer
        MaterialTheme.colorScheme.tertiary -> MaterialTheme.colorScheme.onTertiary
        MaterialTheme.colorScheme.onTertiary -> MaterialTheme.colorScheme.tertiary
        MaterialTheme.colorScheme.tertiaryContainer -> MaterialTheme.colorScheme.onTertiaryContainer
        MaterialTheme.colorScheme.onTertiaryContainer -> MaterialTheme.colorScheme.tertiaryContainer
        MaterialTheme.colorScheme.error -> MaterialTheme.colorScheme.onError
        MaterialTheme.colorScheme.errorContainer -> MaterialTheme.colorScheme.onErrorContainer
        MaterialTheme.colorScheme.onError -> MaterialTheme.colorScheme.error
        MaterialTheme.colorScheme.onErrorContainer -> MaterialTheme.colorScheme.errorContainer
        MaterialTheme.colorScheme.background -> MaterialTheme.colorScheme.onBackground
        MaterialTheme.colorScheme.onBackground -> MaterialTheme.colorScheme.background
        MaterialTheme.colorScheme.surface -> MaterialTheme.colorScheme.onSurface
        MaterialTheme.colorScheme.onSurface -> MaterialTheme.colorScheme.surface
        MaterialTheme.colorScheme.surfaceVariant -> MaterialTheme.colorScheme.onSurfaceVariant
        MaterialTheme.colorScheme.onSurfaceVariant -> MaterialTheme.colorScheme.surfaceVariant
        MaterialTheme.colorScheme.outline -> MaterialTheme.colorScheme.outline
        MaterialTheme.colorScheme.inverseOnSurface -> MaterialTheme.colorScheme.inverseSurface
        MaterialTheme.colorScheme.inverseSurface -> MaterialTheme.colorScheme.surface
        MaterialTheme.colorScheme.inversePrimary -> MaterialTheme.colorScheme.primary
        MaterialTheme.colorScheme.surfaceTint -> MaterialTheme.colorScheme.surface
        MaterialTheme.colorScheme.outlineVariant -> MaterialTheme.colorScheme.outlineVariant
        MaterialTheme.colorScheme.scrim -> MaterialTheme.colorScheme.scrim

        else -> MaterialTheme.colorScheme.primary
    }
}