package com.ryinex.accountant.client.shared.presentation.users

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.checkbox.AppCheckbox
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.feature.components.adaptWidth
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.domain.users.home.ViewModel
import com.ryinex.accountant.client.shared.domain.users.home.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.app
import com.ryinex.accountant.client.shared.data.common.utilities.validInteger
import com.ryinex.accountant.client.shared.presentation.common.dialogs.DelayConfirmDialog
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import kotlin.time.Duration.Companion.seconds

internal fun LazyStaggeredGridScope.UserListSection(
    state: ScreenState,
    users: List<User>,
    viewModel: ViewModel
) {

    users.forEach { user ->
        item {
            UserCard(
                modifier = Modifier.adaptWidth(),
                user = user,
                state = state,
                viewModel = viewModel
            )
        }
    }
}

@Composable
private fun UserCard(
    modifier: Modifier = Modifier,
    state: ScreenState,
    user: User,
    viewModel: ViewModel
) {
    val navigator = LocalNavigator.current
    var showClearDialog by remember { mutableStateOf(false) }
    var showMoveDialog by remember { mutableStateOf(false) }
    var showAddDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    AppCard(
        modifier = modifier.shadowBluish(),
        padding = Dimensions.Paddings.medium.dp
    ) {
        AppColumn(
            modifier = Modifier,
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSubSectionTitleText(
                text = user.name,
                color = MaterialTheme.colorScheme.primary,
            )

            AppLabeledBodyTextHorizontal(
                label = "نوع المستخدم",
                text = if (user.id == state.organization?.ownerId) "المالك" else if (user.isAdmin) "مسؤول" else "مستخدم"
            )

            HorizontalDivider()

            AppLabeledBodyTextHorizontal(
                label = "العهدة",
                text = user.balance.formatted(),
                ltrText = true
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {

                AppTextButton(
                    modifier = Modifier,
                    text = "المزيد",
                    enabled = state.isAddUserEnabled,
                    onClick = { navigator?.browserPush(UserTransactionsScreen(user, state.organization!!)) }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "سحب",
                    enabled = user.balance > 0.0.app(),
                    onClick = { showMoveDialog = true }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "إضافة",
                    enabled = true,
                    onClick = { showAddDialog = true }
                )
            }

            HorizontalDivider()

            AppTextButton(
                modifier = Modifier,
                text = "حذف المستخدم",
                enabled = viewModel.isDeletedEnabled(user),
                onClick = { showDeleteDialog = true }
            )
        }
    }

    if (showMoveDialog) {
        RemoveBottomSheet(
            user = user,
            onDismissRequest = {
                showMoveDialog = false
            },
            onMoveCustody = { amount ->
                viewModel.decreaseUserCustody(user.id, amount)
            }
        )
    }

    if (showAddDialog) {
        AddBottomSheet(
            user = user,
            onDismissRequest = {
                showAddDialog = false
            },
            onAddCustody = { amount ->
                viewModel.increaseUserCustody(user.id, amount)
            },
        )
    }

    if (showDeleteDialog) {
        DeleteUser(
            user = user,
            onDismissRequest = { showDeleteDialog = false },
            onDeleteUser = { viewModel.removeUser(user.id) }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddBottomSheet(
    user: User,
    onAddCustody: (AppDouble) -> Unit,
    onDismissRequest: () -> Unit
) {
    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var amount by remember { mutableStateOf(0.0.app()) }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = "إضافة إلى العهدة"
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "إضافة",
                    enabled = amount > 0.0.app(),
                    onClick = {
                        onAddCustody(amount)
                        onDismissRequest()
                    }
                )
            }

            AppDecimalTextField(
                modifier = Modifier.fillMaxWidth(),
                amount = amount,
                hint = "القيمة",
                enabled = true,
                isError = amount <= 0.0,
                onlyPositive = true,
                onValueChange = { amount = it },
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun RemoveBottomSheet(
    user: User,
    onMoveCustody: (AppDouble) -> Unit,
    onDismissRequest: () -> Unit
) {
    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var amount by remember { mutableStateOf(0.0.app()) }
        val isAmountError by remember(amount) { mutableStateOf(amount > user.balance) }
        val isEnabled by remember(amount, isAmountError) { mutableStateOf(amount > 0.0 && !isAmountError) }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = "سحب من العهدة"
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "سحب",
                    enabled = isEnabled,
                    onClick = {
                        onMoveCustody(amount)
                        onDismissRequest()
                    }
                )
            }

            AppDecimalTextField(
                modifier = Modifier.fillMaxWidth(),
                amount = amount,
                hint = "القيمة",
                enabled = true,
                isError = isAmountError,
                onlyPositive = true,
                onValueChange = { amount = it },
                errorText = "القيمة يجب ان لا تزيد عن قيمة العهدة"
            )
        }
    }
}

@Composable
private fun DeleteUser(
    user: User,
    onDeleteUser: () -> Unit,
    onDismissRequest: () -> Unit
) {
    DelayConfirmDialog(
        title = "حذف المستخدم",
        message = "هل أنت متأكد من حذف المستخدم (${user.name}) ؟",
        onConfirm = {
            onDeleteUser()
            onDismissRequest()
        },
        onDismiss = onDismissRequest,
        confirmContainerColor = MaterialTheme.colorScheme.error,
        confirmTextColor = MaterialTheme.colorScheme.onError,
        delay = 5.seconds
    )
}

@Composable
internal fun AddUserSheet(
    modifier: Modifier = Modifier,
    viewModel: ViewModel,
    onDismissRequest: () -> Unit
) {
    val minNameLength = remember { 4 }
    val minPhoneNumberLength = remember { 11 }
    val minUsernameLength = remember { 3 }
    val minPasswordLength = remember { 6 }

    AppBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismissRequest
    ) {
        var name by remember { mutableStateOf("") }
        var phoneNumber by remember { mutableStateOf("") }
        var isAdmin by remember { mutableStateOf(false) }

        var username by remember { mutableStateOf("") }
        var password by remember { mutableStateOf("") }

        val isNameError by remember(name) { mutableStateOf(name.trim().length < minNameLength) }
        val isPhoneNumberError by remember(phoneNumber) { mutableStateOf(phoneNumber.trim().length < minPhoneNumberLength) }

        val isUsernameError by remember(username) { mutableStateOf(username.trim().length < minUsernameLength) }
        val isPasswordError by remember(password) { mutableStateOf(password.trim().length < minPasswordLength) }

        val isEnabled by remember(isNameError, isPhoneNumberError, isUsernameError, isPasswordError) {
            mutableStateOf(!isNameError && !isPhoneNumberError && !isUsernameError && !isPasswordError)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = "إضافة مستخدم"
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "إضافة",
                    enabled = isEnabled,
                    onClick = {
                        viewModel.addUser(
                            username = username,
                            name = name,
                            phoneNumber = phoneNumber,
                            secondaryPhoneNumber = phoneNumber,
                            isAdmin = isAdmin,
                            password = password
                        )
                        onDismissRequest()
                    }
                )
            }

            HorizontalDivider()

            AppSubSectionTitleText(
                modifier = Modifier,
                text = "بيانات المستخدم"
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = name,
                hint = "الإسم",
                enabled = true,
                isError = isNameError,
                onValueChange = { name = it },
                errorText = "المستخدم يجب أن يكون على الأقل $minNameLength أحرف"
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = phoneNumber,
                hint = "رقم الهاتف",
                enabled = true,
                maxLength = minPhoneNumberLength,
                isError = isPhoneNumberError,
                onValueChange = {
                    if (it.validInteger()) {
                        phoneNumber = it.trim()
                    }
                },
                errorText = "رقم الهاتف يجب أن يكون على الأقل $minPhoneNumberLength أرقام",
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.NumberPassword)
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Start),
                alignment = Alignment.CenterVertically
            ) {
                AppCheckbox(
                    modifier = Modifier,
                    checked = isAdmin,
                    onCheckedChange = { isAdmin = it }
                )

                AppBodyText(text = "مسؤول")
            }

            HorizontalDivider()

            AppSubSectionTitleText(
                modifier = Modifier,
                text = "بيانات تسجيل الدخول"
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = username,
                hint = "المستخدم",
                enabled = true,
                isError = isUsernameError,
                onValueChange = { username = it },
                errorText = "المستخدم يجب أن يكون على الأقل $minNameLength أحرف",
                onValueValidation = { it.isEmpty() || it.length == it.trim().length }
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = password,
                hint = "كلمة المرور",
                enabled = true,
                isError = isPasswordError,
                onValueChange = { password = it },
                errorText = "كلمة المرور يجب أن تكون على الأقل $minPasswordLength أحرف",
                onValueValidation = { it.isEmpty() || it.length == it.trim().length }
            )
        }
    }
}
