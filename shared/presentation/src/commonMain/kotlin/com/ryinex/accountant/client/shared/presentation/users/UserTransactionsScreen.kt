package com.ryinex.accountant.client.shared.presentation.users

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.domain.common.components.userType
import com.ryinex.accountant.client.shared.domain.users.balance.ViewModel
import com.ryinex.accountant.client.shared.domain.users.balance.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppSurface
import com.ryinex.accountant.client.shared.presentation.common.tables.userTransactionsTable
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import kotlinx.coroutines.launch

class UserTransactionsScreen(private val user: User, private val organization: Organization) : BrowserScreen {

    override val urlSegment: String = "transactions"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel { ViewModel(TempDI.status, TempDI.users, emptyCoroutineContext) }
        val state = viewModel.screenState.stream.collectAsState(Samples.initTransactionsState).value
        val scope = rememberCoroutineScope()

        AppBasePage(
            title = "المستخدمين",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = true,
            isPullToRefreshEnabled = true,
            isPullToRefresh = false,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = { navigator?.browserPop() },
        ) {
            LaunchedEffect(Unit) { viewModel.initScreen(user, organization) }

            UserTransactionsScreenContent(
                state = state,
                viewModel = viewModel
            )

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }

    @Composable
    private fun UserTransactionsScreenContent(
        modifier: Modifier = Modifier,
        state: ScreenState,
        viewModel: ViewModel
    ) {
        val isDesktop = LocalIsDesktop.current
        val lazyState = rememberLazyListState()
        val table = userTransactionsTable(
            lazyState = lazyState,
            items = state.transactions
        )

        AppLazyColumn(
            modifier = modifier.fillMaxSize(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            lazyState = lazyState,
            alignment = Alignment.Start
        ) { horizontalScrollState ->

            item { Spacer(modifier = Modifier.sectionsSpacing()) }

            item {
                AppSurface(
                    modifier = Modifier.shadowBluish(),
                    padding = PaddingValues(Dimensions.Paddings.medium.dp),
                ) {
                    AppColumn(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                        alignment = Alignment.Start
                    ) {
                        AppRow(
                            modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
                            arrangement = Arrangement.SpaceBetween,
                            alignment = Alignment.CenterVertically
                        ) {

                            Box(
                                modifier = Modifier.fillMaxHeight().weight(1f),
                                contentAlignment = Alignment.CenterStart
                            ) {
                                AppSubSectionTitleText(
                                    state.user?.name ?: "",
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }

                        AppLabeledBodyTextHorizontal(
                            label = "نوع المستخدم",
                            text = state.user?.userType(state.organization) ?: ""
                        )

                        AppLabeledBodyTextHorizontal(label = "العهدة", text = state.user?.balance?.formatted() ?: "")
                    }
                }
            }

            item {
                AppSectionTitleText(text = "حركة التحويلات", modifier = Modifier.sectionsSpacing())
            }

            UserTransactionsSection(
                state = state,
                isDesktop = isDesktop,
                scrollableState = horizontalScrollState,
                table = table
            )
        }
    }
}
