package com.ryinex.accountant.client.shared.presentation.users

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.users.models.BalanceTransaction
import com.ryinex.accountant.client.shared.domain.users.balance.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.feature.components.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledLabelTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.kotlin.datatable.data.DataTable
import com.ryinex.kotlin.datatable.views.EmbeddedDataTableView
import core.common.extensions.dateFormat

internal fun LazyListScope.UserTransactionsSection(
    state: ScreenState,
    isDesktop: Boolean,
    scrollableState: ScrollState,
    table: DataTable<BalanceTransaction>
) {

    if (isDesktop) {
        EmbeddedDataTableView(scrollableState, table)
    } else {
        for (item in state.transactions) {
            item {
                TransactionCard(
                    amount = item.amount,
                    currentAmount = item.currentAmount,
                    description = item.description,
                    createdBy = item.createdByUser.name,
                    createdAtIseoFormat = item.createdAt
                )
            }
        }
    }
}

@Composable
private fun TransactionCard(
    amount: AppDouble,
    currentAmount: AppDouble,
    description: String,
    createdBy: String,
    createdAtIseoFormat: String,
    modifier: Modifier = Modifier
) {
    AppCard(
        modifier = modifier.fillMaxWidth().shadowBluish()
    ) {
        var extended by remember { mutableStateOf(false) }

        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { extended = !extended }
                .padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.Start
        ) {


            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
                alignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    contentAlignment = if (LayoutDirection.Rtl == LocalLayoutDirection.current) Alignment.CenterStart else Alignment.CenterEnd
                ) {
                    ForceLtr {
                        AppTitleText(
                            text = amount.formatted(),
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }


                AppSupportImageViewButton(
                    painter = rememberVectorPainter(if (extended) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown),
                    enabled = true,
                    onClick = { extended = !extended },
                    backgroundColor = Color.Transparent,
                    tint = MaterialTheme.colorScheme.tertiary
                )
            }

            AppColumn(
                modifier = Modifier,
                arrangement = Arrangement.Top,
                alignment = Alignment.Start
            ) {
                if (description.isNotBlank()) {
                    AppLabelText(
                        text = description,
                        color = LocalContentColor.current
                    )
                } else {
                    AppLabeledLabelTextHorizontal(
                        label = "بواسطة",
                        text = createdBy
                    )
                }

                if (extended) {
                    if (description.isNotBlank()) {
                        AppLabeledLabelTextHorizontal(
                            label = "بواسطة",
                            text = createdBy
                        )
                    }

                    AppLabeledLabelTextHorizontal(
                        label = "بتاريخ",
                        text = createdAtIseoFormat.dateFormat(),
                        ltrText = true
                    )

                    AppLabeledLabelTextHorizontal(
                        label = "المتاح حالياً",
                        text = currentAmount.formatted(),
                        ltrText = true
                    )
                }
            }
        }
    }
}