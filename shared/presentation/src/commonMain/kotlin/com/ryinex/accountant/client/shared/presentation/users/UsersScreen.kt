package com.ryinex.accountant.client.shared.presentation.users

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_add
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppFAB
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppLazyStaggeredGrid
import com.ryinex.accountant.client.shared.presentation.common.feature.components.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.emptyCoroutineContext
import com.ryinex.accountant.client.shared.domain.users.home.ViewModel
import com.ryinex.accountant.client.shared.domain.users.home.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource


class UsersScreen : BrowserScreen {

    override val urlSegment: String = "users"

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(TempDI.status, TempDI.users, TempDI.organizations, emptyCoroutineContext)
        }
        val state = viewModel.screenState.stream.collectAsState(Samples.initState).value
        val scope = rememberCoroutineScope()
        var showAddSheet by remember { mutableStateOf(false) }

        AppBasePage(
            title = "المستخدمين",
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp),
            isBackEnabled = true,
            isPullToRefreshEnabled = true,
            isPullToRefresh = false,
            onPullToRefresh = { viewModel.refreshScreen() },
            onBack = { navigator?.browserPop() },
            fab = {
                AddFab(
                    isEnabled = state.isAddUserEnabled,
                    onClick = { showAddSheet = true }
                )
            }
        ) {
            LaunchedEffect(Unit) { viewModel.initScreen() }

            UsersScreenContent(
                state = state,
                viewModel = viewModel
            )

            if (showAddSheet) {
                AddSheet(
                    viewModel = viewModel,
                    onDismissRequest = { showAddSheet = false }
                )
            }

            AppStatusBar(
                statuses = state.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }
    }


    @Composable
    private fun UsersScreenContent(
        modifier: Modifier = Modifier,
        state: ScreenState,
        viewModel: ViewModel
    ) {
        AppLazyStaggeredGrid(
            modifier = modifier.fillMaxSize(),
            alignment = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            spacing = Dimensions.Paddings.medium.dp,
        ) {

            item(
                span = StaggeredGridItemSpan.FullLine
            ) {
                var showAddSheet by remember { mutableStateOf(false) }
                AppRow(
                    modifier = Modifier.sectionsSpacing(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {
                    AppSectionTitleText(text = "")

                    AppTextButton(
                        modifier = Modifier,
                        text = "إضافة",
                        enabled = state.isAddUserEnabled,
                        iconPainter = painterResource(Res.drawable.ic_add),
                        onClick = { showAddSheet = true }
                    )
                }

                if (showAddSheet) {
                    AddSheet(
                        viewModel = viewModel,
                        onDismissRequest = { showAddSheet = false }
                    )
                }

            }

            UserListSection(
                state = state,
                state.users,
                viewModel = viewModel
            )

            item {
                Spacer(modifier = Modifier.sectionsSpacing())
            }
        }
    }

    @Composable
    private fun AddSheet(
        viewModel: ViewModel,
        onDismissRequest: () -> Unit
    ) {
        AddUserSheet(
            onDismissRequest = onDismissRequest,
            viewModel = viewModel
        )
    }

    @Composable
    private fun AddFab(
        isEnabled: Boolean,
        onClick: () -> Unit
    ) {
        AppFAB(
            enabled = isEnabled,
            onClick = onClick,
            icon = painterResource(Res.drawable.ic_add)
        )
    }
}
